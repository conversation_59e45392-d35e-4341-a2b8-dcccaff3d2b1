const mongoose = require("mongoose")

const fraClaimSchema = new mongoose.Schema({
  holderName: {
    type: String,
    required: true,
  },
  village: {
    type: String,
    required: true,
  },
  district: {
    type: String,
    required: true,
  },
  state: {
    type: String,
    required: true,
  },
  claimType: {
    type: String,
    enum: ["IFR", "CR", "CFR"],
    required: true,
  },
  coordinates: {
    type: {
      type: String,
      enum: ["Point", "Polygon"],
      required: true,
    },
    coordinates: {
      type: mongoose.Schema.Types.Mixed,
      required: true,
    },
  },
  status: {
    type: String,
    enum: ["Pending", "Approved", "Rejected"],
    default: "Pending",
  },
  createdAt: {
    type: Date,
    default: Date.now,
  },
})

fraClaimSchema.index({ coordinates: "2dsphere" })

module.exports = mongoose.model("FraClaim", fraClaimSchema)
