const mongoose = require("mongoose")

const schemeSchema = new mongoose.Schema({
  name: {
    type: String,
    required: true,
  },
  description: {
    type: String,
    required: true,
  },
  eligibility: {
    type: [String],
    required: true,
  },
  category: {
    type: String,
    enum: ["Water", "Agriculture", "Forest", "Livelihood", "Infrastructure"],
    required: true,
  },
  targetBeneficiaries: {
    type: String,
    required: true,
  },
  isActive: {
    type: Boolean,
    default: true,
  },
})

module.exports = mongoose.model("Scheme", schemeSchema)
