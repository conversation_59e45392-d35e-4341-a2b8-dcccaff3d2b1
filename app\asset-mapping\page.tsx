"use client"

import { Navigation } from "@/components/navigation"
import { SatelliteUpload } from "@/components/satellite-upload"
import { ClassificationResults } from "@/components/classification-results"
import { ClassificationHistory } from "@/components/classification-history"
import { ClassificationProcessing } from "@/components/classification-processing"
import { useState } from "react"

export interface ClassificationResult {
  id: string
  fileName: string
  village: string
  district: string
  state: string
  totalArea: number
  classification: {
    forest: { area: number; percentage: number; color: string }
    water: { area: number; percentage: number; color: string }
    agricultural: { area: number; percentage: number; color: string }
    residential: { area: number; percentage: number; color: string }
    barren: { area: number; percentage: number; color: string }
  }
  confidence: number
  processingTime: string
  timestamp: Date
  status: "processing" | "completed" | "error"
  originalImage?: string
  classifiedImage?: string
}

export default function AssetMappingPage() {
  const [currentProcessing, setCurrentProcessing] = useState<{
    fileName: string
    progress: number
    status: "uploading" | "analyzing" | "classifying" | "completed" | "error"
    currentStep: string
  } | null>(null)

  const [results, setResults] = useState<ClassificationResult[]>([])
  const [selectedResult, setSelectedResult] = useState<ClassificationResult | null>(null)

  const handleImageUpload = async (file: File, metadata: { village: string; district: string; state: string }) => {
    const fileName = file.name
    setCurrentProcessing({
      fileName,
      progress: 0,
      status: "uploading",
      currentStep: "Uploading satellite image...",
    })

    // Create object URL for the uploaded image
    const imageUrl = URL.createObjectURL(file)

    // Simulate upload and processing steps
    const steps = [
      { progress: 20, status: "uploading" as const, step: "Uploading satellite image..." },
      { progress: 40, status: "analyzing" as const, step: "Analyzing image resolution and quality..." },
      { progress: 60, status: "analyzing" as const, step: "Preprocessing image data..." },
      { progress: 80, status: "classifying" as const, step: "Running AI classification model..." },
      { progress: 95, status: "classifying" as const, step: "Generating land use map..." },
      { progress: 100, status: "completed" as const, step: "Classification completed!" },
    ]

    for (const step of steps) {
      await new Promise((resolve) => setTimeout(resolve, 800))
      setCurrentProcessing((prev) => prev && { ...prev, ...step, currentStep: step.step })
    }

    // Mock classification result
    const mockResult: ClassificationResult = {
      id: Date.now().toString(),
      fileName,
      village: metadata.village,
      district: metadata.district,
      state: metadata.state,
      totalArea: 1250.5,
      classification: {
        forest: { area: 750.2, percentage: 60.0, color: "#228B22" },
        water: { area: 125.1, percentage: 10.0, color: "#4169E1" },
        agricultural: { area: 250.1, percentage: 20.0, color: "#FFD700" },
        residential: { area: 75.1, percentage: 6.0, color: "#FF6347" },
        barren: { area: 50.0, percentage: 4.0, color: "#D2691E" },
      },
      confidence: 0.92,
      processingTime: "15.7s",
      timestamp: new Date(),
      status: "completed",
      originalImage: imageUrl,
      classifiedImage: "/satellite-image-with-color-coded-land-use-classifi.jpg",
    }

    setResults((prev) => [mockResult, ...prev])
    setSelectedResult(mockResult)

    // Clear processing status after a delay
    setTimeout(() => {
      setCurrentProcessing(null)
    }, 2000)
  }

  return (
    <div className="min-h-screen bg-background">
      <Navigation />

      <main className="container mx-auto px-4 py-8">
        <div className="mb-8">
          <h1 className="text-3xl font-bold tracking-tight mb-2">Asset Mapping</h1>
          <p className="text-muted-foreground">
            Upload satellite images for AI-powered land use classification and asset mapping
          </p>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
          {/* Upload and Results Section */}
          <div className="lg:col-span-2 space-y-6">
            <SatelliteUpload onImageUpload={handleImageUpload} />

            {currentProcessing && <ClassificationProcessing processing={currentProcessing} />}

            {selectedResult && (
              <ClassificationResults
                result={selectedResult}
                onResultUpdate={(updatedResult) => {
                  setResults((prev) => prev.map((r) => (r.id === updatedResult.id ? updatedResult : r)))
                  setSelectedResult(updatedResult)
                }}
              />
            )}
          </div>

          {/* History Section */}
          <div>
            <ClassificationHistory
              results={results}
              selectedResult={selectedResult}
              onResultSelect={setSelectedResult}
            />
          </div>
        </div>
      </main>
    </div>
  )
}
