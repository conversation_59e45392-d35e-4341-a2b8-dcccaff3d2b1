"use client"

import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Checkbox } from "@/components/ui/checkbox"
import { Badge } from "@/components/ui/badge"
import { Button } from "@/components/ui/button"
import type { MapFilters } from "@/app/fra-atlas/page"
import { Layers, Filter, RotateCcw } from "lucide-react"

interface MapSidebarProps {
  filters: MapFilters
  setFilters: (filters: MapFilters) => void
  selectedLayers: {
    boundaries: boolean
    fraClaims: boolean
    landUse: boolean
  }
  setSelectedLayers: (layers: any) => void
}

const states = ["Madhya Pradesh", "Odisha", "Telangana", "Tripura"]

const districts = {
  "Madhya Pradesh": ["Mandla", "Balaghat", "Dindori"],
  Odisha: ["Dhenkanal", "Mayurbhanj", "Keonjhar"],
  Telangana: ["Mulug<PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "Jayashankar"],
  Tripura: ["Dhalai", "North Tripura", "South Tripura"],
}

const villages = {
  Mandla: ["Kanha Village", "Bamni Village", "Ghughri"],
  Dhenkanal: ["Bamni Village", "Kapilash", "Hindol"],
  Mulugu: ["Eturnagaram", "Mulugu", "Venkatapur"],
}

export function MapSidebar({ filters, setFilters, selectedLayers, setSelectedLayers }: MapSidebarProps) {
  const handleFilterChange = (key: keyof MapFilters, value: string) => {
    const newFilters = { ...filters, [key]: value }

    // Reset dependent filters
    if (key === "state") {
      newFilters.district = ""
      newFilters.village = ""
    } else if (key === "district") {
      newFilters.village = ""
    }

    setFilters(newFilters)
  }

  const handleLayerToggle = (layer: string, checked: boolean) => {
    setSelectedLayers({
      ...selectedLayers,
      [layer]: checked,
    })
  }

  const resetFilters = () => {
    setFilters({
      state: "",
      district: "",
      village: "",
      claimType: "",
      status: "",
    })
  }

  const availableDistricts = filters.state ? districts[filters.state as keyof typeof districts] || [] : []
  const availableVillages = filters.district ? villages[filters.district as keyof typeof villages] || [] : []

  return (
    <div className="w-80 border-r bg-background p-4 overflow-y-auto">
      {/* Filters Section */}
      <Card className="mb-4">
        <CardHeader>
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-2">
              <Filter className="h-4 w-4" />
              <CardTitle className="text-lg">Filters</CardTitle>
            </div>
            <Button variant="ghost" size="sm" onClick={resetFilters}>
              <RotateCcw className="h-4 w-4" />
            </Button>
          </div>
          <CardDescription>Filter FRA claims by location and type</CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div>
            <label className="text-sm font-medium mb-2 block">State</label>
            <Select value={filters.state} onValueChange={(value) => handleFilterChange("state", value)}>
              <SelectTrigger>
                <SelectValue placeholder="Select state" />
              </SelectTrigger>
              <SelectContent>
                {states.map((state) => (
                  <SelectItem key={state} value={state}>
                    {state}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>

          <div>
            <label className="text-sm font-medium mb-2 block">District</label>
            <Select
              value={filters.district}
              onValueChange={(value) => handleFilterChange("district", value)}
              disabled={!filters.state}
            >
              <SelectTrigger>
                <SelectValue placeholder="Select district" />
              </SelectTrigger>
              <SelectContent>
                {availableDistricts.map((district) => (
                  <SelectItem key={district} value={district}>
                    {district}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>

          <div>
            <label className="text-sm font-medium mb-2 block">Village</label>
            <Select
              value={filters.village}
              onValueChange={(value) => handleFilterChange("village", value)}
              disabled={!filters.district}
            >
              <SelectTrigger>
                <SelectValue placeholder="Select village" />
              </SelectTrigger>
              <SelectContent>
                {availableVillages.map((village) => (
                  <SelectItem key={village} value={village}>
                    {village}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>

          <div>
            <label className="text-sm font-medium mb-2 block">Claim Type</label>
            <Select value={filters.claimType} onValueChange={(value) => handleFilterChange("claimType", value)}>
              <SelectTrigger>
                <SelectValue placeholder="All claim types" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="IFR">Individual Forest Rights (IFR)</SelectItem>
                <SelectItem value="CFR">Community Forest Rights (CFR)</SelectItem>
                <SelectItem value="CR">Community Rights (CR)</SelectItem>
              </SelectContent>
            </Select>
          </div>

          <div>
            <label className="text-sm font-medium mb-2 block">Status</label>
            <Select value={filters.status} onValueChange={(value) => handleFilterChange("status", value)}>
              <SelectTrigger>
                <SelectValue placeholder="All statuses" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="Pending">Pending</SelectItem>
                <SelectItem value="Approved">Approved</SelectItem>
                <SelectItem value="Rejected">Rejected</SelectItem>
              </SelectContent>
            </Select>
          </div>
        </CardContent>
      </Card>

      {/* Layers Section */}
      <Card className="mb-4">
        <CardHeader>
          <div className="flex items-center space-x-2">
            <Layers className="h-4 w-4" />
            <CardTitle className="text-lg">Map Layers</CardTitle>
          </div>
          <CardDescription>Toggle map layers visibility</CardDescription>
        </CardHeader>
        <CardContent className="space-y-3">
          <div className="flex items-center space-x-2">
            <Checkbox
              id="boundaries"
              checked={selectedLayers.boundaries}
              onCheckedChange={(checked) => handleLayerToggle("boundaries", checked as boolean)}
            />
            <label htmlFor="boundaries" className="text-sm font-medium">
              State/District Boundaries
            </label>
          </div>

          <div className="flex items-center space-x-2">
            <Checkbox
              id="fraClaims"
              checked={selectedLayers.fraClaims}
              onCheckedChange={(checked) => handleLayerToggle("fraClaims", checked as boolean)}
            />
            <label htmlFor="fraClaims" className="text-sm font-medium">
              FRA Claims
            </label>
          </div>

          <div className="flex items-center space-x-2">
            <Checkbox
              id="landUse"
              checked={selectedLayers.landUse}
              onCheckedChange={(checked) => handleLayerToggle("landUse", checked as boolean)}
            />
            <label htmlFor="landUse" className="text-sm font-medium">
              Land Use Classification
            </label>
          </div>
        </CardContent>
      </Card>

      {/* Legend */}
      <Card>
        <CardHeader>
          <CardTitle className="text-lg">Legend</CardTitle>
        </CardHeader>
        <CardContent className="space-y-2">
          <div className="flex items-center space-x-2">
            <div className="w-4 h-4 bg-green-500 rounded-full"></div>
            <span className="text-sm">Approved Claims</span>
          </div>
          <div className="flex items-center space-x-2">
            <div className="w-4 h-4 bg-yellow-500 rounded-full"></div>
            <span className="text-sm">Pending Claims</span>
          </div>
          <div className="flex items-center space-x-2">
            <div className="w-4 h-4 bg-red-500 rounded-full"></div>
            <span className="text-sm">Rejected Claims</span>
          </div>
          <div className="flex items-center space-x-2">
            <div className="w-4 h-4 bg-blue-500 opacity-30"></div>
            <span className="text-sm">State Boundaries</span>
          </div>
          <div className="flex items-center space-x-2">
            <div className="w-4 h-4 bg-purple-500 opacity-30"></div>
            <span className="text-sm">District Boundaries</span>
          </div>
        </CardContent>
      </Card>

      {/* Summary Stats */}
      <div className="mt-4 space-y-2">
        <div className="flex justify-between items-center">
          <span className="text-sm text-muted-foreground">Total Claims:</span>
          <Badge variant="secondary">2,547</Badge>
        </div>
        <div className="flex justify-between items-center">
          <span className="text-sm text-muted-foreground">Filtered:</span>
          <Badge variant="default">156</Badge>
        </div>
      </div>
    </div>
  )
}
