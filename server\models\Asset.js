const mongoose = require("mongoose")

const assetSchema = new mongoose.Schema({
  village: {
    type: String,
    required: true,
  },
  district: {
    type: String,
    required: true,
  },
  state: {
    type: String,
    required: true,
  },
  landUseStats: {
    forest: {
      type: Number,
      default: 0,
    },
    water: {
      type: Number,
      default: 0,
    },
    agricultural: {
      type: Number,
      default: 0,
    },
    residential: {
      type: Number,
      default: 0,
    },
    barren: {
      type: Number,
      default: 0,
    },
  },
  lastUpdated: {
    type: Date,
    default: Date.now,
  },
})

module.exports = mongoose.model("Asset", assetSchema)
