"use client"

import { <PERSON>, Card<PERSON>ontent, CardDescription, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON>itle } from "@/components/ui/card"
import { Progress } from "@/components/ui/progress"
import { Badge } from "@/components/ui/badge"
import { Upload, Satellite, Brain, CheckCircle, XCircle } from "lucide-react"

interface ClassificationProcessingProps {
  processing: {
    fileName: string
    progress: number
    status: "uploading" | "analyzing" | "classifying" | "completed" | "error"
    currentStep: string
  }
}

export function ClassificationProcessing({ processing }: ClassificationProcessingProps) {
  const getStatusIcon = () => {
    switch (processing.status) {
      case "uploading":
        return <Upload className="h-4 w-4" />
      case "analyzing":
        return <Satellite className="h-4 w-4 animate-pulse" />
      case "classifying":
        return <Brain className="h-4 w-4 animate-pulse" />
      case "completed":
        return <CheckCircle className="h-4 w-4 text-green-500" />
      case "error":
        return <XCircle className="h-4 w-4 text-red-500" />
    }
  }

  const getStatusColor = () => {
    switch (processing.status) {
      case "uploading":
        return "secondary"
      case "analyzing":
        return "secondary"
      case "classifying":
        return "secondary"
      case "completed":
        return "default"
      case "error":
        return "destructive"
    }
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center space-x-2">
          {getStatusIcon()}
          <span>AI Classification in Progress</span>
        </CardTitle>
        <CardDescription>Processing satellite image with machine learning models</CardDescription>
      </CardHeader>
      <CardContent className="space-y-4">
        <div className="flex items-center justify-between">
          <span className="text-sm font-medium">{processing.fileName}</span>
          <Badge variant={getStatusColor() as any}>{processing.status}</Badge>
        </div>

        <Progress value={processing.progress} className="w-full" />

        <div className="text-sm text-muted-foreground">{processing.currentStep}</div>

        {processing.status === "analyzing" && (
          <div className="space-y-2 text-xs text-muted-foreground">
            <div className="flex items-center space-x-2">
              <div className="w-2 h-2 bg-green-500 rounded-full"></div>
              <span>Image quality assessment completed</span>
            </div>
            <div className="flex items-center space-x-2">
              <div className="w-2 h-2 bg-yellow-500 rounded-full animate-pulse"></div>
              <span>Preprocessing spectral bands</span>
            </div>
            <div className="flex items-center space-x-2">
              <div className="w-2 h-2 bg-gray-300 rounded-full"></div>
              <span>Preparing for classification</span>
            </div>
          </div>
        )}

        {processing.status === "classifying" && (
          <div className="space-y-2 text-xs text-muted-foreground">
            <div className="flex items-center space-x-2">
              <div className="w-2 h-2 bg-green-500 rounded-full"></div>
              <span>Image preprocessing completed</span>
            </div>
            <div className="flex items-center space-x-2">
              <div className="w-2 h-2 bg-green-500 rounded-full"></div>
              <span>Spectral analysis completed</span>
            </div>
            <div className="flex items-center space-x-2">
              <div className="w-2 h-2 bg-yellow-500 rounded-full animate-pulse"></div>
              <span>Running deep learning classification</span>
            </div>
            <div className="flex items-center space-x-2">
              <div className="w-2 h-2 bg-gray-300 rounded-full"></div>
              <span>Generating land use map</span>
            </div>
          </div>
        )}

        {processing.status === "completed" && (
          <div className="p-3 bg-green-50 dark:bg-green-950 rounded-lg">
            <div className="flex items-center space-x-2 text-green-700 dark:text-green-300">
              <CheckCircle className="h-4 w-4" />
              <span className="text-sm font-medium">Classification completed successfully!</span>
            </div>
            <p className="text-xs text-green-600 dark:text-green-400 mt-1">
              Land use classification has been generated with high confidence. Results are ready for review.
            </p>
          </div>
        )}
      </CardContent>
    </Card>
  )
}
