"use client"

import type React from "react"

import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON><PERSON>er, CardTitle } from "@/components/ui/card"
import { TrendingUp, TrendingDown, MapPin, FileCheck, Satellite, Users } from "lucide-react"

interface StatCardProps {
  title: string
  value: string | number
  description: string
  trend?: "up" | "down" | "neutral"
  trendValue?: string
  icon: React.ReactNode
}

function StatCard({ title, value, description, trend, trendValue, icon }: StatCardProps) {
  return (
    <Card>
      <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
        <CardTitle className="text-sm font-medium">{title}</CardTitle>
        {icon}
      </CardHeader>
      <CardContent>
        <div className="text-2xl font-bold">{value}</div>
        <div className="flex items-center space-x-2 text-xs text-muted-foreground">
          <span>{description}</span>
          {trend && trendValue && (
            <div className="flex items-center space-x-1">
              {trend === "up" ? (
                <TrendingUp className="h-3 w-3 text-green-500" />
              ) : trend === "down" ? (
                <TrendingDown className="h-3 w-3 text-red-500" />
              ) : null}
              <span className={trend === "up" ? "text-green-500" : trend === "down" ? "text-red-500" : ""}>
                {trendValue}
              </span>
            </div>
          )}
        </div>
      </CardContent>
    </Card>
  )
}

export function DashboardStats() {
  return (
    <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
      <StatCard
        title="Total FRA Claims"
        value="2,547"
        description="Across all states"
        trend="up"
        trendValue="+12% from last month"
        icon={<MapPin className="h-4 w-4 text-muted-foreground" />}
      />
      <StatCard
        title="Documents Processed"
        value="1,823"
        description="OCR digitization complete"
        trend="up"
        trendValue="+8% from last month"
        icon={<FileCheck className="h-4 w-4 text-muted-foreground" />}
      />
      <StatCard
        title="Satellite Images Analyzed"
        value="456"
        description="AI classification done"
        trend="up"
        trendValue="+15% from last month"
        icon={<Satellite className="h-4 w-4 text-muted-foreground" />}
      />
      <StatCard
        title="Beneficiaries Identified"
        value="12,340"
        description="Through DSS recommendations"
        trend="up"
        trendValue="+23% from last month"
        icon={<Users className="h-4 w-4 text-muted-foreground" />}
      />
    </div>
  )
}
