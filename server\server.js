const express = require("express")
const mongoose = require("mongoose")
const cors = require("cors")
const helmet = require("helmet")
require("dotenv").config()

const app = express()
const PORT = process.env.PORT || 5000

// Middleware
app.use(helmet())
app.use(cors())
app.use(express.json())
app.use(express.urlencoded({ extended: true }))

// MongoDB Connection
mongoose
  .connect(process.env.MONGODB_URI || "mongodb://localhost:27017/fra-atlas", {
    useNewUrlParser: true,
    useUnifiedTopology: true,
  })
  .then(() => console.log("MongoDB connected successfully"))
  .catch((err) => console.error("MongoDB connection error:", err))

// Routes
app.use("/api/ocr", require("./routes/ocr"))
app.use("/api/assets", require("./routes/assets"))
app.use("/api/dss", require("./routes/dss"))
app.use("/api/fra-claims", require("./routes/fraClaims"))

// Health check endpoint
app.get("/api/health", (req, res) => {
  res.json({ status: "OK", message: "FRA Atlas & DSS Server is running" })
})

app.listen(PORT, () => {
  console.log(`Server running on port ${PORT}`)
})
