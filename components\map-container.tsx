"use client"

import { useEffect, useRef, useState } from "react"
import { Card } from "@/components/ui/card"
import { But<PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import type { MapFilters } from "@/app/fra-atlas/page"
import { ZoomIn, ZoomOut, Home, Maximize } from "lucide-react"

interface MapContainerProps {
  filters: MapFilters
  selectedLayers: {
    boundaries: boolean
    fraClaims: boolean
    landUse: boolean
  }
}

// Mock FRA Claims data
const mockFRAClaims = [
  {
    id: 1,
    holderName: "Ramesh <PERSON> Singh",
    village: "Kanha Village",
    district: "Mandla",
    state: "Madhya Pradesh",
    claimType: "IFR",
    status: "Approved",
    coordinates: [80.1674, 22.2587],
    area: "2.5 hectares",
  },
  {
    id: 2,
    holderName: "Sunita Devi",
    village: "Bamni Village",
    district: "Dhenkanal",
    state: "Odisha",
    claimType: "CFR",
    status: "Pending",
    coordinates: [85.5956, 20.6593],
    area: "15.2 hectares",
  },
  {
    id: 3,
    holderName: "Ravi Naik",
    village: "Eturnagaram",
    district: "Mulugu",
    state: "Telangana",
    claimType: "CR",
    status: "Approved",
    coordinates: [80.0982, 18.3232],
    area: "8.7 hectares",
  },
]

export function MapContainer({ filters, selectedLayers }: MapContainerProps) {
  const mapRef = useRef<HTMLDivElement>(null)
  const [map, setMap] = useState<any>(null)
  const [selectedClaim, setSelectedClaim] = useState<any>(null)
  const [isFullscreen, setIsFullscreen] = useState(false)

  useEffect(() => {
    if (typeof window !== "undefined" && mapRef.current && !map) {
      // Dynamically import Leaflet to avoid SSR issues
      import("leaflet").then((L) => {
        // Fix for default markers in Leaflet with webpack
        delete (L.Icon.Default.prototype as any)._getIconUrl
        L.Icon.Default.mergeOptions({
          iconRetinaUrl: "https://cdnjs.cloudflare.com/ajax/libs/leaflet/1.7.1/images/marker-icon-2x.png",
          iconUrl: "https://cdnjs.cloudflare.com/ajax/libs/leaflet/1.7.1/images/marker-icon.png",
          shadowUrl: "https://cdnjs.cloudflare.com/ajax/libs/leaflet/1.7.1/images/marker-shadow.png",
        })

        const mapInstance = L.map(mapRef.current!).setView([20.5937, 78.9629], 5)

        // Add OpenStreetMap tiles
        L.tileLayer("https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png", {
          attribution: "© OpenStreetMap contributors",
        }).addTo(mapInstance)

        setMap(mapInstance)
      })
    }

    return () => {
      if (map) {
        map.remove()
        setMap(null)
      }
    }
  }, [])

  useEffect(() => {
    if (map && selectedLayers.fraClaims) {
      // Clear existing markers
      map.eachLayer((layer: any) => {
        if (layer.options && layer.options.pane === "markerPane") {
          map.removeLayer(layer)
        }
      })

      // Filter claims based on current filters
      const filteredClaims = mockFRAClaims.filter((claim) => {
        if (filters.state && claim.state !== filters.state) return false
        if (filters.district && claim.district !== filters.district) return false
        if (filters.village && claim.village !== filters.village) return false
        if (filters.claimType && claim.claimType !== filters.claimType) return false
        if (filters.status && claim.status !== filters.status) return false
        return true
      })

      // Add markers for filtered claims
      import("leaflet").then((L) => {
        filteredClaims.forEach((claim) => {
          const color = claim.status === "Approved" ? "green" : claim.status === "Pending" ? "orange" : "red"

          const marker = L.circleMarker([claim.coordinates[1], claim.coordinates[0]], {
            radius: 8,
            fillColor: color,
            color: "#fff",
            weight: 2,
            opacity: 1,
            fillOpacity: 0.8,
          }).addTo(map)

          marker.bindPopup(`
            <div class="p-2">
              <h3 class="font-semibold">${claim.holderName}</h3>
              <p class="text-sm text-gray-600">${claim.village}, ${claim.district}</p>
              <p class="text-sm"><strong>Type:</strong> ${claim.claimType}</p>
              <p class="text-sm"><strong>Status:</strong> ${claim.status}</p>
              <p class="text-sm"><strong>Area:</strong> ${claim.area}</p>
            </div>
          `)

          marker.on("click", () => {
            setSelectedClaim(claim)
          })
        })
      })
    }
  }, [map, selectedLayers, filters])

  const handleZoomIn = () => {
    if (map) map.zoomIn()
  }

  const handleZoomOut = () => {
    if (map) map.zoomOut()
  }

  const handleResetView = () => {
    if (map) map.setView([20.5937, 78.9629], 5)
  }

  const toggleFullscreen = () => {
    setIsFullscreen(!isFullscreen)
  }

  return (
    <div className={`relative ${isFullscreen ? "fixed inset-0 z-50 bg-background" : "h-full"}`}>
      {/* Map Controls */}
      <div className="absolute top-4 right-4 z-10 flex flex-col space-y-2">
        <Button size="sm" variant="secondary" onClick={handleZoomIn}>
          <ZoomIn className="h-4 w-4" />
        </Button>
        <Button size="sm" variant="secondary" onClick={handleZoomOut}>
          <ZoomOut className="h-4 w-4" />
        </Button>
        <Button size="sm" variant="secondary" onClick={handleResetView}>
          <Home className="h-4 w-4" />
        </Button>
        <Button size="sm" variant="secondary" onClick={toggleFullscreen}>
          <Maximize className="h-4 w-4" />
        </Button>
      </div>

      {/* Selected Claim Info */}
      {selectedClaim && (
        <Card className="absolute top-4 left-4 z-10 w-80 p-4">
          <div className="flex justify-between items-start mb-2">
            <h3 className="font-semibold">{selectedClaim.holderName}</h3>
            <Button size="sm" variant="ghost" onClick={() => setSelectedClaim(null)}>
              ×
            </Button>
          </div>
          <div className="space-y-2 text-sm">
            <p>
              <strong>Location:</strong> {selectedClaim.village}, {selectedClaim.district}, {selectedClaim.state}
            </p>
            <p>
              <strong>Claim Type:</strong> {selectedClaim.claimType}
            </p>
            <p>
              <strong>Area:</strong> {selectedClaim.area}
            </p>
            <div className="flex items-center space-x-2">
              <strong>Status:</strong>
              <Badge
                variant={
                  selectedClaim.status === "Approved"
                    ? "default"
                    : selectedClaim.status === "Pending"
                      ? "secondary"
                      : "destructive"
                }
              >
                {selectedClaim.status}
              </Badge>
            </div>
          </div>
        </Card>
      )}

      {/* Map Container */}
      <div ref={mapRef} className="w-full h-full" style={{ minHeight: isFullscreen ? "100vh" : "600px" }} />

      {/* Loading State */}
      {!map && (
        <div className="absolute inset-0 flex items-center justify-center bg-muted">
          <div className="text-center">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto mb-2"></div>
            <p className="text-sm text-muted-foreground">Loading map...</p>
          </div>
        </div>
      )}
    </div>
  )
}
