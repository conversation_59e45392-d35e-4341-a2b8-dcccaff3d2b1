"use client"

import { Navigation } from "@/components/navigation"
import { VillageSelector } from "@/components/village-selector"
import { SocioEconomicStats } from "@/components/socio-economic-stats"
import { SchemeRecommendations } from "@/components/scheme-recommendations"
import { DSSComparison } from "@/components/dss-comparison"
import { useState } from "react"

export interface VillageData {
  village: string
  district: string
  state: string
  socioEconomicStats: {
    population: number
    households: number
    literacyRate: number
    waterAccess: number
    electricityAccess: number
    forestCover: number
    agriculturalLand: number
    roadConnectivity: number
    healthcareFacilities: number
    schoolsCount: number
  }
  recommendations: SchemeRecommendation[]
}

export interface SchemeRecommendation {
  scheme: string
  priority: "High" | "Medium" | "Low"
  reason: string
  description: string
  eligibility: string[]
  estimatedBeneficiaries: number
  category: "Water" | "Agriculture" | "Forest" | "Livelihood" | "Infrastructure" | "Health" | "Education"
  implementationCost?: string
  timeframe?: string
}

const mockVillageData: Record<string, VillageData> = {
  "Kanha Village": {
    village: "Kanha Village",
    district: "Mandla",
    state: "Madhya Pradesh",
    socioEconomicStats: {
      population: 1250,
      households: 280,
      literacyRate: 68.5,
      waterAccess: 45.2,
      electricityAccess: 78.3,
      forestCover: 60.0,
      agriculturalLand: 20.0,
      roadConnectivity: 65.0,
      healthcareFacilities: 1,
      schoolsCount: 2,
    },
    recommendations: [
      {
        scheme: "Jal Jeevan Mission",
        priority: "High",
        reason: "Low water access (45.2%) - Below national average",
        description: "Provides functional household tap connections to every rural household",
        eligibility: ["Rural households", "Water scarce areas"],
        estimatedBeneficiaries: 280,
        category: "Water",
        implementationCost: "₹2.8 Crores",
        timeframe: "12-18 months",
      },
      {
        scheme: "Forest Rights Act Implementation",
        priority: "High",
        reason: "High forest cover (60%) with tribal population",
        description: "Recognition of forest rights and occupation in forest land",
        eligibility: ["Scheduled Tribes", "Traditional forest dwellers"],
        estimatedBeneficiaries: 200,
        category: "Forest",
        implementationCost: "₹50 Lakhs",
        timeframe: "6-12 months",
      },
      {
        scheme: "PM-KISAN",
        priority: "Medium",
        reason: "Significant agricultural land (20%) with farming households",
        description: "Income support to farmer families for agriculture and allied activities",
        eligibility: ["Small and marginal farmers", "Landholding farmers"],
        estimatedBeneficiaries: 150,
        category: "Agriculture",
        implementationCost: "₹18 Lakhs/year",
        timeframe: "Ongoing",
      },
      {
        scheme: "Pradhan Mantri Gram Sadak Yojana",
        priority: "Medium",
        reason: "Moderate road connectivity (65%) needs improvement",
        description: "Provides all-weather road connectivity to unconnected habitations",
        eligibility: ["Rural habitations", "Population > 500"],
        estimatedBeneficiaries: 1250,
        category: "Infrastructure",
        implementationCost: "₹1.5 Crores",
        timeframe: "18-24 months",
      },
    ],
  },
  "Bamni Village": {
    village: "Bamni Village",
    district: "Dhenkanal",
    state: "Odisha",
    socioEconomicStats: {
      population: 890,
      households: 195,
      literacyRate: 72.8,
      waterAccess: 82.1,
      electricityAccess: 91.5,
      forestCover: 45.0,
      agriculturalLand: 30.0,
      roadConnectivity: 85.0,
      healthcareFacilities: 2,
      schoolsCount: 3,
    },
    recommendations: [
      {
        scheme: "PM-KISAN",
        priority: "High",
        reason: "High agricultural land (30%) with active farming community",
        description: "Income support to farmer families for agriculture and allied activities",
        eligibility: ["Small and marginal farmers", "Landholding farmers"],
        estimatedBeneficiaries: 180,
        category: "Agriculture",
        implementationCost: "₹21.6 Lakhs/year",
        timeframe: "Ongoing",
      },
      {
        scheme: "Ayushman Bharat",
        priority: "Medium",
        reason: "Limited healthcare facilities (2) for population size",
        description: "Health insurance scheme providing coverage up to ₹5 lakhs per family per year",
        eligibility: ["Below Poverty Line families", "Rural households"],
        estimatedBeneficiaries: 195,
        category: "Health",
        implementationCost: "₹15 Lakhs/year",
        timeframe: "Immediate",
      },
      {
        scheme: "Skill India Mission",
        priority: "Low",
        reason: "Good literacy rate (72.8%) provides foundation for skill development",
        description: "Skill development and vocational training programs",
        eligibility: ["Youth aged 15-45", "Rural population"],
        estimatedBeneficiaries: 120,
        category: "Livelihood",
        implementationCost: "₹8 Lakhs",
        timeframe: "6-12 months",
      },
    ],
  },
  Eturnagaram: {
    village: "Eturnagaram",
    district: "Mulugu",
    state: "Telangana",
    socioEconomicStats: {
      population: 2150,
      households: 485,
      literacyRate: 58.3,
      waterAccess: 38.7,
      electricityAccess: 72.1,
      forestCover: 70.0,
      agriculturalLand: 15.0,
      roadConnectivity: 55.0,
      healthcareFacilities: 1,
      schoolsCount: 2,
    },
    recommendations: [
      {
        scheme: "Jal Jeevan Mission",
        priority: "High",
        reason: "Very low water access (38.7%) - Critical need",
        description: "Provides functional household tap connections to every rural household",
        eligibility: ["Rural households", "Water scarce areas"],
        estimatedBeneficiaries: 485,
        category: "Water",
        implementationCost: "₹4.85 Crores",
        timeframe: "12-18 months",
      },
      {
        scheme: "Samagra Shiksha Abhiyan",
        priority: "High",
        reason: "Low literacy rate (58.3%) requires educational intervention",
        description: "Integrated scheme for school education covering pre-school to class XII",
        eligibility: ["Government schools", "Rural areas"],
        estimatedBeneficiaries: 350,
        category: "Education",
        implementationCost: "₹75 Lakhs",
        timeframe: "Ongoing",
      },
      {
        scheme: "Forest Rights Act Implementation",
        priority: "High",
        reason: "Highest forest cover (70%) with tribal communities",
        description: "Recognition of forest rights and occupation in forest land",
        eligibility: ["Scheduled Tribes", "Traditional forest dwellers"],
        estimatedBeneficiaries: 400,
        category: "Forest",
        implementationCost: "₹1.2 Crores",
        timeframe: "6-12 months",
      },
    ],
  },
}

export default function DSSPage() {
  const [selectedVillage, setSelectedVillage] = useState<string>("")
  const [comparisonVillages, setComparisonVillages] = useState<string[]>([])

  const villageData = selectedVillage ? mockVillageData[selectedVillage] : null

  return (
    <div className="min-h-screen bg-background">
      <Navigation />

      <main className="container mx-auto px-4 py-8">
        <div className="mb-8">
          <h1 className="text-3xl font-bold tracking-tight mb-2">Decision Support System</h1>
          <p className="text-muted-foreground">
            Data-driven recommendations for policy implementation and scheme allocation based on village-level
            socio-economic indicators
          </p>
        </div>

        <div className="space-y-6">
          <VillageSelector
            selectedVillage={selectedVillage}
            onVillageSelect={setSelectedVillage}
            availableVillages={Object.keys(mockVillageData)}
            comparisonVillages={comparisonVillages}
            onComparisonChange={setComparisonVillages}
          />

          {villageData && (
            <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
              <div className="lg:col-span-2 space-y-6">
                <SocioEconomicStats data={villageData} />
                <SchemeRecommendations recommendations={villageData.recommendations} villageData={villageData} />
              </div>

              <div>
                <DSSComparison
                  selectedVillage={villageData}
                  comparisonVillages={comparisonVillages.map((v) => mockVillageData[v]).filter(Boolean)}
                />
              </div>
            </div>
          )}
        </div>
      </main>
    </div>
  )
}
