const mongoose = require("mongoose")
const FraClaim = require("../models/FraClaim")
const Asset = require("../models/Asset")
const Scheme = require("../models/Scheme")
require("dotenv").config()

// Connect to MongoDB
mongoose
  .connect(process.env.MONGODB_URI || "mongodb://localhost:27017/fra-atlas")
  .then(() => console.log("Connected to MongoDB for seeding"))
  .catch((err) => console.error("MongoDB connection error:", err))

const seedData = async () => {
  try {
    // Clear existing data
    await FraClaim.deleteMany({})
    await Asset.deleteMany({})
    await Scheme.deleteMany({})

    // Seed FRA Claims
    const fraClaims = [
      {
        holderName: "Ramesh Kumar Singh",
        village: "Kanha Village",
        district: "Mandla",
        state: "Madhya Pradesh",
        claimType: "IFR",
        coordinates: { type: "Point", coordinates: [80.1674, 22.2587] },
        status: "Approved",
      },
      {
        holderName: "Sunita Devi",
        village: "Bamni Village",
        district: "Dhenkanal",
        state: "Odisha",
        claimType: "CFR",
        coordinates: { type: "Point", coordinates: [85.5956, 20.6593] },
        status: "Pending",
      },
      {
        holderName: "Ravi Naik",
        village: "Eturnagaram",
        district: "Mulugu",
        state: "Telangana",
        claimType: "CR",
        coordinates: { type: "Point", coordinates: [80.0982, 18.3232] },
        status: "Approved",
      },
    ]

    // Seed Assets
    const assets = [
      {
        village: "Kanha Village",
        district: "Mandla",
        state: "Madhya Pradesh",
        landUseStats: { forest: 60.0, water: 10.0, agricultural: 20.0, residential: 6.0, barren: 4.0 },
      },
      {
        village: "Bamni Village",
        district: "Dhenkanal",
        state: "Odisha",
        landUseStats: { forest: 45.0, water: 15.0, agricultural: 30.0, residential: 7.0, barren: 3.0 },
      },
      {
        village: "Eturnagaram",
        district: "Mulugu",
        state: "Telangana",
        landUseStats: { forest: 70.0, water: 8.0, agricultural: 15.0, residential: 5.0, barren: 2.0 },
      },
    ]

    // Seed Schemes
    const schemes = [
      {
        name: "Jal Jeevan Mission",
        description: "Provides functional household tap connections to every rural household",
        eligibility: ["Rural households", "Water scarce areas"],
        category: "Water",
        targetBeneficiaries: "Rural households without piped water supply",
      },
      {
        name: "PM-KISAN",
        description: "Income support to farmer families for agriculture and allied activities",
        eligibility: ["Small and marginal farmers", "Landholding farmers"],
        category: "Agriculture",
        targetBeneficiaries: "Small and marginal farmer families",
      },
      {
        name: "Forest Rights Act Implementation",
        description: "Recognition of forest rights and occupation in forest land",
        eligibility: ["Scheduled Tribes", "Traditional forest dwellers"],
        category: "Forest",
        targetBeneficiaries: "Tribal and traditional forest dwelling communities",
      },
    ]

    await FraClaim.insertMany(fraClaims)
    await Asset.insertMany(assets)
    await Scheme.insertMany(schemes)

    console.log("Database seeded successfully!")
    process.exit(0)
  } catch (error) {
    console.error("Seeding failed:", error)
    process.exit(1)
  }
}

seedData()
