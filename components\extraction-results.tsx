"use client"

import { <PERSON>, Card<PERSON>ontent, CardDescription, Card<PERSON><PERSON><PERSON>, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Textarea } from "@/components/ui/textarea"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Badge } from "@/components/ui/badge"
import { <PERSON><PERSON>, <PERSON>bsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { useState } from "react"
import type { OCRResult } from "@/app/digitization/page"
import { FileText, Download, Save, Edit3, Eye } from "lucide-react"

interface ExtractionResultsProps {
  result: OCRResult
  onResultUpdate: (result: OCRResult) => void
}

export function ExtractionResults({ result, onResultUpdate }: ExtractionResultsProps) {
  const [isEditing, setIsEditing] = useState(false)
  const [editedResult, setEditedResult] = useState(result)

  const handleSave = () => {
    onResultUpdate(editedResult)
    setIsEditing(false)
  }

  const handleCancel = () => {
    setEditedResult(result)
    setIsEditing(false)
  }

  const handleExport = () => {
    const exportData = {
      fileName: result.fileName,
      extractedData: {
        holderName: result.holderName,
        village: result.village,
        district: result.district,
        state: result.state,
        claimType: result.claimType,
      },
      metadata: {
        confidence: result.confidence,
        processingTime: result.processingTime,
        timestamp: result.timestamp,
      },
    }

    const blob = new Blob([JSON.stringify(exportData, null, 2)], { type: "application/json" })
    const url = URL.createObjectURL(blob)
    const a = document.createElement("a")
    a.href = url
    a.download = `${result.fileName}_extracted_data.json`
    document.body.appendChild(a)
    a.click()
    document.body.removeChild(a)
    URL.revokeObjectURL(url)
  }

  return (
    <Card>
      <CardHeader>
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-2">
            <FileText className="h-5 w-5" />
            <CardTitle>Extraction Results</CardTitle>
          </div>
          <div className="flex items-center space-x-2">
            <Badge variant="secondary">Confidence: {Math.round(result.confidence * 100)}%</Badge>
            <Button variant="outline" size="sm" onClick={() => setIsEditing(!isEditing)}>
              {isEditing ? <Eye className="h-4 w-4" /> : <Edit3 className="h-4 w-4" />}
            </Button>
            <Button variant="outline" size="sm" onClick={handleExport}>
              <Download className="h-4 w-4" />
            </Button>
          </div>
        </div>
        <CardDescription>
          Extracted data from {result.fileName} • Processed in {result.processingTime}
        </CardDescription>
      </CardHeader>
      <CardContent>
        <Tabs defaultValue="structured" className="w-full">
          <TabsList className="grid w-full grid-cols-2">
            <TabsTrigger value="structured">Structured Data</TabsTrigger>
            <TabsTrigger value="raw">Raw Text</TabsTrigger>
          </TabsList>

          <TabsContent value="structured" className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <Label htmlFor="holderName">Patta Holder Name</Label>
                {isEditing ? (
                  <Input
                    id="holderName"
                    value={editedResult.holderName}
                    onChange={(e) => setEditedResult({ ...editedResult, holderName: e.target.value })}
                  />
                ) : (
                  <div className="p-2 bg-muted rounded text-sm">{result.holderName}</div>
                )}
              </div>

              <div>
                <Label htmlFor="village">Village Name</Label>
                {isEditing ? (
                  <Input
                    id="village"
                    value={editedResult.village}
                    onChange={(e) => setEditedResult({ ...editedResult, village: e.target.value })}
                  />
                ) : (
                  <div className="p-2 bg-muted rounded text-sm">{result.village}</div>
                )}
              </div>

              <div>
                <Label htmlFor="district">District</Label>
                {isEditing ? (
                  <Input
                    id="district"
                    value={editedResult.district}
                    onChange={(e) => setEditedResult({ ...editedResult, district: e.target.value })}
                  />
                ) : (
                  <div className="p-2 bg-muted rounded text-sm">{result.district}</div>
                )}
              </div>

              <div>
                <Label htmlFor="state">State</Label>
                {isEditing ? (
                  <Input
                    id="state"
                    value={editedResult.state}
                    onChange={(e) => setEditedResult({ ...editedResult, state: e.target.value })}
                  />
                ) : (
                  <div className="p-2 bg-muted rounded text-sm">{result.state}</div>
                )}
              </div>

              <div className="md:col-span-2">
                <Label htmlFor="claimType">Claim Type</Label>
                {isEditing ? (
                  <Select
                    value={editedResult.claimType}
                    onValueChange={(value: "IFR" | "CR" | "CFR") =>
                      setEditedResult({ ...editedResult, claimType: value })
                    }
                  >
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="IFR">Individual Forest Rights (IFR)</SelectItem>
                      <SelectItem value="CFR">Community Forest Rights (CFR)</SelectItem>
                      <SelectItem value="CR">Community Rights (CR)</SelectItem>
                    </SelectContent>
                  </Select>
                ) : (
                  <div className="p-2 bg-muted rounded text-sm">
                    {result.claimType} -{" "}
                    {result.claimType === "IFR"
                      ? "Individual Forest Rights"
                      : result.claimType === "CFR"
                        ? "Community Forest Rights"
                        : "Community Rights"}
                  </div>
                )}
              </div>
            </div>

            {isEditing && (
              <div className="flex space-x-2 pt-4">
                <Button onClick={handleSave}>
                  <Save className="h-4 w-4 mr-2" />
                  Save Changes
                </Button>
                <Button variant="outline" onClick={handleCancel}>
                  Cancel
                </Button>
              </div>
            )}
          </TabsContent>

          <TabsContent value="raw">
            <div>
              <Label htmlFor="extractedText">Raw Extracted Text</Label>
              <Textarea
                id="extractedText"
                value={result.extractedText}
                readOnly
                className="min-h-[300px] font-mono text-sm"
              />
            </div>
          </TabsContent>
        </Tabs>
      </CardContent>
    </Card>
  )
}
