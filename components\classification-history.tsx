"use client"

import { <PERSON>, <PERSON><PERSON><PERSON>nt, CardDescription, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON>itle } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { ScrollArea } from "@/components/ui/scroll-area"
import type { ClassificationResult } from "@/app/asset-mapping/page"
import { Satellite, Clock, CheckCircle, XCircle, MapPin } from "lucide-react"

interface ClassificationHistoryProps {
  results: ClassificationResult[]
  selectedResult: ClassificationResult | null
  onResultSelect: (result: ClassificationResult) => void
}

export function ClassificationHistory({ results, selectedResult, onResultSelect }: ClassificationHistoryProps) {
  const getStatusIcon = (status: string) => {
    switch (status) {
      case "completed":
        return <CheckCircle className="h-4 w-4 text-green-500" />
      case "processing":
        return <Clock className="h-4 w-4 text-yellow-500" />
      case "error":
        return <XCircle className="h-4 w-4 text-red-500" />
      default:
        return <Satellite className="h-4 w-4" />
    }
  }

  const formatDate = (date: Date) => {
    return new Intl.DateTimeFormat("en-US", {
      month: "short",
      day: "numeric",
      hour: "2-digit",
      minute: "2-digit",
    }).format(date)
  }

  const getDominantLandUse = (classification: ClassificationResult["classification"]) => {
    return Object.entries(classification).reduce(
      (max, [type, data]) =>
        data.percentage > (classification[max as keyof typeof classification]?.percentage || 0) ? type : max,
      Object.keys(classification)[0],
    )
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center space-x-2">
          <Clock className="h-5 w-5" />
          <span>Classification History</span>
        </CardTitle>
        <CardDescription>Recent satellite image classifications ({results.length} total)</CardDescription>
      </CardHeader>
      <CardContent>
        <ScrollArea className="h-[600px]">
          {results.length === 0 ? (
            <div className="text-center py-8 text-muted-foreground">
              <Satellite className="h-8 w-8 mx-auto mb-2 opacity-50" />
              <p>No images classified yet</p>
              <p className="text-sm">Upload a satellite image to get started</p>
            </div>
          ) : (
            <div className="space-y-3">
              {results.map((result) => (
                <div
                  key={result.id}
                  className={`p-3 rounded-lg border cursor-pointer transition-colors ${
                    selectedResult?.id === result.id ? "border-primary bg-primary/5" : "border-border hover:bg-muted/50"
                  }`}
                  onClick={() => onResultSelect(result)}
                >
                  <div className="flex items-start justify-between mb-2">
                    <div className="flex items-center space-x-2">
                      {getStatusIcon(result.status)}
                      <span className="text-sm font-medium truncate">{result.fileName}</span>
                    </div>
                    <Badge variant="secondary" className="text-xs">
                      {Math.round(result.confidence * 100)}%
                    </Badge>
                  </div>

                  <div className="space-y-1 text-xs text-muted-foreground">
                    <div className="flex items-center space-x-1">
                      <MapPin className="h-3 w-3" />
                      <span>
                        {result.village}, {result.district}
                      </span>
                    </div>
                    <p>
                      <strong>Area:</strong> {result.totalArea} hectares
                    </p>
                    <p>
                      <strong>Dominant:</strong> {getDominantLandUse(result.classification)} (
                      {
                        result.classification[
                          getDominantLandUse(result.classification) as keyof typeof result.classification
                        ]?.percentage
                      }
                      %)
                    </p>
                  </div>

                  {/* Mini land use breakdown */}
                  <div className="flex space-x-1 mt-2">
                    {Object.entries(result.classification).map(([type, data]) => (
                      <div
                        key={type}
                        className="h-2 rounded-full"
                        style={{
                          backgroundColor: data.color,
                          width: `${Math.max(data.percentage, 2)}%`,
                        }}
                        title={`${type}: ${data.percentage}%`}
                      />
                    ))}
                  </div>

                  <div className="flex items-center justify-between mt-2 text-xs text-muted-foreground">
                    <span>{formatDate(result.timestamp)}</span>
                    <span>{result.processingTime}</span>
                  </div>
                </div>
              ))}
            </div>
          )}
        </ScrollArea>

        {results.length > 0 && (
          <div className="mt-4 pt-4 border-t">
            <Button variant="outline" size="sm" className="w-full bg-transparent">
              Export All Classifications
            </Button>
          </div>
        )}
      </CardContent>
    </Card>
  )
}
