"use client"

import { <PERSON>, CardContent, CardDescription, Card<PERSON><PERSON>er, CardTitle } from "@/components/ui/card"
import { Progress } from "@/components/ui/progress"
import { Badge } from "@/components/ui/badge"
import type { VillageData } from "@/app/dss/page"
import { BarChart3, TrendingUp, TrendingDown, Minus } from "lucide-react"

interface DSSComparisonProps {
  selectedVillage: VillageData
  comparisonVillages: VillageData[]
}

export function DSSComparison({ selectedVillage, comparisonVillages }: DSSComparisonProps) {
  const indicators = [
    { key: "waterAccess", label: "Water Access", unit: "%" },
    { key: "electricityAccess", label: "Electricity", unit: "%" },
    { key: "literacyRate", label: "Literacy Rate", unit: "%" },
    { key: "roadConnectivity", label: "Road Connectivity", unit: "%" },
  ]

  const getComparisonIcon = (current: number, comparison: number) => {
    if (current > comparison) return <TrendingUp className="h-3 w-3 text-green-500" />
    if (current < comparison) return <TrendingDown className="h-3 w-3 text-red-500" />
    return <Minus className="h-3 w-3 text-gray-500" />
  }

  const getRecommendationSummary = (village: VillageData) => {
    const high = village.recommendations.filter((r) => r.priority === "High").length
    const medium = village.recommendations.filter((r) => r.priority === "Medium").length
    const low = village.recommendations.filter((r) => r.priority === "Low").length
    return { high, medium, low, total: village.recommendations.length }
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center space-x-2">
          <BarChart3 className="h-5 w-5" />
          <span>Village Comparison</span>
        </CardTitle>
        <CardDescription>
          Compare {selectedVillage.village} with other villages
          {comparisonVillages.length === 0 && " (select villages to compare)"}
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-6">
        {comparisonVillages.length === 0 ? (
          <div className="text-center py-8 text-muted-foreground">
            <BarChart3 className="h-8 w-8 mx-auto mb-2 opacity-50" />
            <p>No villages selected for comparison</p>
            <p className="text-sm">Add villages using the selector above</p>
          </div>
        ) : (
          <>
            {/* Key Indicators Comparison */}
            <div>
              <h3 className="text-sm font-medium mb-3">Key Indicators</h3>
              <div className="space-y-3">
                {indicators.map((indicator) => (
                  <div key={indicator.key}>
                    <div className="flex justify-between text-xs mb-1">
                      <span>{indicator.label}</span>
                      <span>
                        {
                          selectedVillage.socioEconomicStats[
                            indicator.key as keyof typeof selectedVillage.socioEconomicStats
                          ]
                        }
                        {indicator.unit}
                      </span>
                    </div>
                    <Progress
                      value={
                        selectedVillage.socioEconomicStats[
                          indicator.key as keyof typeof selectedVillage.socioEconomicStats
                        ] as number
                      }
                      className="h-2 mb-2"
                    />

                    {comparisonVillages.map((village) => {
                      const currentValue = selectedVillage.socioEconomicStats[
                        indicator.key as keyof typeof selectedVillage.socioEconomicStats
                      ] as number
                      const comparisonValue = village.socioEconomicStats[
                        indicator.key as keyof typeof village.socioEconomicStats
                      ] as number

                      return (
                        <div
                          key={village.village}
                          className="flex items-center justify-between text-xs text-muted-foreground"
                        >
                          <div className="flex items-center space-x-1">
                            {getComparisonIcon(currentValue, comparisonValue)}
                            <span>{village.village}</span>
                          </div>
                          <span>
                            {comparisonValue}
                            {indicator.unit}
                          </span>
                        </div>
                      )
                    })}
                  </div>
                ))}
              </div>
            </div>

            {/* Recommendations Summary */}
            <div>
              <h3 className="text-sm font-medium mb-3">Recommendations Summary</h3>
              <div className="space-y-3">
                <div className="p-3 bg-muted rounded-lg">
                  <div className="flex justify-between items-center mb-2">
                    <span className="text-sm font-medium">{selectedVillage.village}</span>
                    <Badge variant="outline">{getRecommendationSummary(selectedVillage).total} schemes</Badge>
                  </div>
                  <div className="flex space-x-2 text-xs">
                    <Badge variant="destructive" className="text-xs">
                      {getRecommendationSummary(selectedVillage).high} High
                    </Badge>
                    <Badge variant="secondary" className="text-xs">
                      {getRecommendationSummary(selectedVillage).medium} Medium
                    </Badge>
                    <Badge variant="default" className="text-xs">
                      {getRecommendationSummary(selectedVillage).low} Low
                    </Badge>
                  </div>
                </div>

                {comparisonVillages.map((village) => {
                  const summary = getRecommendationSummary(village)
                  return (
                    <div key={village.village} className="p-3 border rounded-lg">
                      <div className="flex justify-between items-center mb-2">
                        <span className="text-sm font-medium">{village.village}</span>
                        <Badge variant="outline" className="text-xs">
                          {summary.total} schemes
                        </Badge>
                      </div>
                      <div className="flex space-x-2 text-xs">
                        <Badge variant="destructive" className="text-xs">
                          {summary.high} High
                        </Badge>
                        <Badge variant="secondary" className="text-xs">
                          {summary.medium} Medium
                        </Badge>
                        <Badge variant="default" className="text-xs">
                          {summary.low} Low
                        </Badge>
                      </div>
                    </div>
                  )
                })}
              </div>
            </div>

            {/* Population Comparison */}
            <div>
              <h3 className="text-sm font-medium mb-3">Demographics</h3>
              <div className="space-y-2">
                <div className="flex justify-between text-sm">
                  <span>{selectedVillage.village}</span>
                  <span>{selectedVillage.socioEconomicStats.population.toLocaleString()} people</span>
                </div>
                {comparisonVillages.map((village) => (
                  <div key={village.village} className="flex justify-between text-sm text-muted-foreground">
                    <span>{village.village}</span>
                    <span>{village.socioEconomicStats.population.toLocaleString()} people</span>
                  </div>
                ))}
              </div>
            </div>
          </>
        )}
      </CardContent>
    </Card>
  )
}
