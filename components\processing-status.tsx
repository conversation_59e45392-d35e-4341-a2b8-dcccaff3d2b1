"use client"

import { <PERSON>, Card<PERSON>ontent, CardDescription, Card<PERSON><PERSON>er, CardTitle } from "@/components/ui/card"
import { Progress } from "@/components/ui/progress"
import { Badge } from "@/components/ui/badge"
import { Loader2, Upload, CheckCircle, XCircle } from "lucide-react"

interface ProcessingStatusProps {
  processing: {
    fileName: string
    progress: number
    status: "uploading" | "processing" | "completed" | "error"
  }
}

export function ProcessingStatus({ processing }: ProcessingStatusProps) {
  const getStatusIcon = () => {
    switch (processing.status) {
      case "uploading":
        return <Upload className="h-4 w-4" />
      case "processing":
        return <Loader2 className="h-4 w-4 animate-spin" />
      case "completed":
        return <CheckCircle className="h-4 w-4 text-green-500" />
      case "error":
        return <XCircle className="h-4 w-4 text-red-500" />
    }
  }

  const getStatusText = () => {
    switch (processing.status) {
      case "uploading":
        return "Uploading document..."
      case "processing":
        return "Processing with OCR..."
      case "completed":
        return "Processing completed!"
      case "error":
        return "Processing failed"
    }
  }

  const getStatusColor = () => {
    switch (processing.status) {
      case "uploading":
        return "secondary"
      case "processing":
        return "secondary"
      case "completed":
        return "default"
      case "error":
        return "destructive"
    }
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center space-x-2">
          {getStatusIcon()}
          <span>Processing Status</span>
        </CardTitle>
        <CardDescription>Real-time processing updates for your document</CardDescription>
      </CardHeader>
      <CardContent className="space-y-4">
        <div className="flex items-center justify-between">
          <span className="text-sm font-medium">{processing.fileName}</span>
          <Badge variant={getStatusColor() as any}>{getStatusText()}</Badge>
        </div>

        <Progress value={processing.progress} className="w-full" />

        <div className="text-xs text-muted-foreground">
          {processing.status === "uploading" && "Uploading file to server..."}
          {processing.status === "processing" && "Extracting text and analyzing document structure..."}
          {processing.status === "completed" && "Document processed successfully. Results are ready for review."}
          {processing.status === "error" && "An error occurred during processing. Please try again."}
        </div>

        {processing.status === "processing" && (
          <div className="space-y-2 text-xs text-muted-foreground">
            <div className="flex items-center space-x-2">
              <div className="w-2 h-2 bg-green-500 rounded-full"></div>
              <span>Text extraction in progress</span>
            </div>
            <div className="flex items-center space-x-2">
              <div className="w-2 h-2 bg-yellow-500 rounded-full animate-pulse"></div>
              <span>Analyzing document structure</span>
            </div>
            <div className="flex items-center space-x-2">
              <div className="w-2 h-2 bg-gray-300 rounded-full"></div>
              <span>Extracting field data</span>
            </div>
          </div>
        )}
      </CardContent>
    </Card>
  )
}
