const express = require("express")
const router = express.Router()

// Mock DSS recommendations endpoint
router.get("/:village", (req, res) => {
  try {
    const { village } = req.params

    // Mock village data and recommendations
    const mockDSSData = {
      village: village,
      district: "Mandla",
      state: "Madhya Pradesh",
      socioEconomicStats: {
        population: 1250,
        households: 280,
        literacyRate: 68.5,
        waterAccess: 45.2,
        electricityAccess: 78.3,
        forestCover: 60.0,
        agriculturalLand: 20.0,
      },
      recommendations: [
        {
          scheme: "Jal Jeevan Mission",
          priority: "High",
          reason: "Low water access (45.2%) - Below national average",
          description: "Provides functional household tap connections to every rural household",
          eligibility: ["Rural households", "Water scarce areas"],
          estimatedBeneficiaries: 280,
        },
        {
          scheme: "PM-KISAN",
          priority: "Medium",
          reason: "Significant agricultural land (20%) with farming households",
          description: "Income support to farmer families for agriculture and allied activities",
          eligibility: ["Small and marginal farmers", "Landholding farmers"],
          estimatedBeneficiaries: 150,
        },
        {
          scheme: "Forest Rights Act Implementation",
          priority: "High",
          reason: "High forest cover (60%) with tribal population",
          description: "Recognition of forest rights and occupation in forest land",
          eligibility: ["Scheduled Tribes", "Traditional forest dwellers"],
          estimatedBeneficiaries: 200,
        },
      ],
    }

    res.json({
      success: true,
      data: mockDSSData,
      message: "DSS recommendations generated successfully",
    })
  } catch (error) {
    res.status(500).json({ error: "DSS processing failed" })
  }
})

module.exports = router
