"use client"

import { <PERSON>, Card<PERSON>ontent, CardDescription, Card<PERSON><PERSON>er, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>ist, TabsTrigger } from "@/components/ui/tabs"
import { Progress } from "@/components/ui/progress"
import type { ClassificationResult } from "@/app/asset-mapping/page"
import { Satellite, Download, BarChart3, Map, ImageIcon } from "lucide-react"
import Image from "next/image"

interface ClassificationResultsProps {
  result: ClassificationResult
  onResultUpdate: (result: ClassificationResult) => void
}

export function ClassificationResults({ result }: ClassificationResultsProps) {
  const handleExport = () => {
    const exportData = {
      fileName: result.fileName,
      location: {
        village: result.village,
        district: result.district,
        state: result.state,
      },
      classification: result.classification,
      totalArea: result.totalArea,
      metadata: {
        confidence: result.confidence,
        processingTime: result.processingTime,
        timestamp: result.timestamp,
      },
    }

    const blob = new Blob([JSON.stringify(exportData, null, 2)], { type: "application/json" })
    const url = URL.createObjectURL(blob)
    const a = document.createElement("a")
    a.href = url
    a.download = `${result.fileName}_classification_results.json`
    document.body.appendChild(a)
    a.click()
    document.body.removeChild(a)
    URL.revokeObjectURL(url)
  }

  const landUseTypes = Object.entries(result.classification)

  return (
    <Card>
      <CardHeader>
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-2">
            <Satellite className="h-5 w-5" />
            <CardTitle>Classification Results</CardTitle>
          </div>
          <div className="flex items-center space-x-2">
            <Badge variant="secondary">Confidence: {Math.round(result.confidence * 100)}%</Badge>
            <Button variant="outline" size="sm" onClick={handleExport}>
              <Download className="h-4 w-4" />
            </Button>
          </div>
        </div>
        <CardDescription>
          Land use classification for {result.village}, {result.district} • Total area: {result.totalArea} hectares •
          Processed in {result.processingTime}
        </CardDescription>
      </CardHeader>
      <CardContent>
        <Tabs defaultValue="visual" className="w-full">
          <TabsList className="grid w-full grid-cols-3">
            <TabsTrigger value="visual">Visual Results</TabsTrigger>
            <TabsTrigger value="statistics">Statistics</TabsTrigger>
            <TabsTrigger value="breakdown">Breakdown</TabsTrigger>
          </TabsList>

          <TabsContent value="visual" className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <h3 className="text-sm font-medium mb-2 flex items-center space-x-2">
                  <ImageIcon className="h-4 w-4" />
                  <span>Original Satellite Image</span>
                </h3>
                <div className="relative aspect-video bg-muted rounded-lg overflow-hidden">
                  {result.originalImage && (
                    <Image
                      src={result.originalImage || "/placeholder.svg"}
                      alt="Original satellite image"
                      fill
                      className="object-cover"
                    />
                  )}
                </div>
              </div>

              <div>
                <h3 className="text-sm font-medium mb-2 flex items-center space-x-2">
                  <Map className="h-4 w-4" />
                  <span>Classified Land Use Map</span>
                </h3>
                <div className="relative aspect-video bg-muted rounded-lg overflow-hidden">
                  {result.classifiedImage && (
                    <Image
                      src={result.classifiedImage || "/placeholder.svg"}
                      alt="Classified land use map"
                      fill
                      className="object-cover"
                    />
                  )}
                </div>
              </div>
            </div>

            {/* Legend */}
            <div className="grid grid-cols-2 md:grid-cols-5 gap-2">
              {landUseTypes.map(([type, data]) => (
                <div key={type} className="flex items-center space-x-2 text-sm">
                  <div className="w-4 h-4 rounded" style={{ backgroundColor: data.color }}></div>
                  <span className="capitalize">{type}</span>
                </div>
              ))}
            </div>
          </TabsContent>

          <TabsContent value="statistics" className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div>
                <h3 className="text-sm font-medium mb-4 flex items-center space-x-2">
                  <BarChart3 className="h-4 w-4" />
                  <span>Area Distribution</span>
                </h3>
                <div className="space-y-3">
                  {landUseTypes.map(([type, data]) => (
                    <div key={type}>
                      <div className="flex justify-between text-sm mb-1">
                        <span className="capitalize">{type}</span>
                        <span>{data.percentage}%</span>
                      </div>
                      <Progress value={data.percentage} className="h-2" />
                      <div className="text-xs text-muted-foreground mt-1">{data.area} hectares</div>
                    </div>
                  ))}
                </div>
              </div>

              <div>
                <h3 className="text-sm font-medium mb-4">Summary Statistics</h3>
                <div className="space-y-3">
                  <div className="flex justify-between">
                    <span className="text-sm text-muted-foreground">Total Area</span>
                    <span className="text-sm font-medium">{result.totalArea} hectares</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-sm text-muted-foreground">Dominant Land Use</span>
                    <span className="text-sm font-medium capitalize">
                      {landUseTypes.reduce(
                        (max, [type, data]) =>
                          data.percentage > (landUseTypes.find(([t]) => t === max)?.[1]?.percentage || 0) ? type : max,
                        landUseTypes[0][0],
                      )}
                    </span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-sm text-muted-foreground">Classification Confidence</span>
                    <span className="text-sm font-medium">{Math.round(result.confidence * 100)}%</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-sm text-muted-foreground">Processing Time</span>
                    <span className="text-sm font-medium">{result.processingTime}</span>
                  </div>
                </div>
              </div>
            </div>
          </TabsContent>

          <TabsContent value="breakdown" className="space-y-4">
            <div className="grid gap-4">
              {landUseTypes.map(([type, data]) => (
                <Card key={type}>
                  <CardContent className="p-4">
                    <div className="flex items-center justify-between mb-2">
                      <div className="flex items-center space-x-2">
                        <div className="w-4 h-4 rounded" style={{ backgroundColor: data.color }}></div>
                        <span className="font-medium capitalize">{type}</span>
                      </div>
                      <Badge variant="secondary">{data.percentage}%</Badge>
                    </div>
                    <div className="grid grid-cols-2 gap-4 text-sm">
                      <div>
                        <span className="text-muted-foreground">Area:</span>
                        <span className="ml-2 font-medium">{data.area} hectares</span>
                      </div>
                      <div>
                        <span className="text-muted-foreground">Coverage:</span>
                        <span className="ml-2 font-medium">{data.percentage}% of total area</span>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
          </TabsContent>
        </Tabs>
      </CardContent>
    </Card>
  )
}
