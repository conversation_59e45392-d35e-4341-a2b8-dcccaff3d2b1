{"name": "fra-atlas-server", "version": "1.0.0", "description": "Backend server for FRA Atlas & DSS", "main": "server.js", "scripts": {"start": "node server.js", "dev": "nodemon server.js", "seed": "node scripts/seedDatabase.js"}, "dependencies": {"express": "^4.18.2", "mongoose": "^7.5.0", "cors": "^2.8.5", "multer": "^1.4.5-lts.1", "dotenv": "^16.3.1", "helmet": "^7.0.0"}, "devDependencies": {"nodemon": "^3.0.1"}}