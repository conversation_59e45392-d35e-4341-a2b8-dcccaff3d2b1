"use client"

import { <PERSON>, CardContent, CardDescription, Card<PERSON><PERSON>er, CardTitle } from "@/components/ui/card"
import { Progress } from "@/components/ui/progress"
import { Badge } from "@/components/ui/badge"
import type { VillageData } from "@/app/dss/page"
import { Users, Home, GraduationCap, Droplets, Zap, Trees, Wheat, Bold as Road, Hospital, School } from "lucide-react"

interface SocioEconomicStatsProps {
  data: VillageData
}

export function SocioEconomicStats({ data }: SocioEconomicStatsProps) {
  const stats = data.socioEconomicStats

  const getStatusColor = (value: number, thresholds: { good: number; average: number }) => {
    if (value >= thresholds.good) return "text-green-600"
    if (value >= thresholds.average) return "text-yellow-600"
    return "text-red-600"
  }

  const getStatusBadge = (value: number, thresholds: { good: number; average: number }) => {
    if (value >= thresholds.good) return <Badge className="bg-green-100 text-green-800">Good</Badge>
    if (value >= thresholds.average) return <Badge className="bg-yellow-100 text-yellow-800">Average</Badge>
    return <Badge className="bg-red-100 text-red-800">Needs Attention</Badge>
  }

  const indicators = [
    {
      label: "Water Access",
      value: stats.waterAccess,
      icon: Droplets,
      unit: "%",
      thresholds: { good: 80, average: 60 },
      description: "Households with access to safe drinking water",
    },
    {
      label: "Electricity Access",
      value: stats.electricityAccess,
      icon: Zap,
      unit: "%",
      thresholds: { good: 90, average: 70 },
      description: "Households with electricity connection",
    },
    {
      label: "Literacy Rate",
      value: stats.literacyRate,
      icon: GraduationCap,
      unit: "%",
      thresholds: { good: 75, average: 60 },
      description: "Population literacy rate",
    },
    {
      label: "Road Connectivity",
      value: stats.roadConnectivity,
      icon: Road,
      unit: "%",
      thresholds: { good: 80, average: 60 },
      description: "All-weather road connectivity",
    },
    {
      label: "Forest Cover",
      value: stats.forestCover,
      icon: Trees,
      unit: "%",
      thresholds: { good: 33, average: 20 },
      description: "Area under forest cover",
    },
    {
      label: "Agricultural Land",
      value: stats.agriculturalLand,
      icon: Wheat,
      unit: "%",
      thresholds: { good: 40, average: 20 },
      description: "Area under cultivation",
    },
  ]

  return (
    <Card>
      <CardHeader>
        <CardTitle>Socio-Economic Indicators</CardTitle>
        <CardDescription>
          Key development indicators for {data.village}, {data.district}, {data.state}
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-6">
        {/* Basic Demographics */}
        <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
          <div className="text-center p-4 bg-muted rounded-lg">
            <Users className="h-6 w-6 mx-auto mb-2 text-muted-foreground" />
            <div className="text-2xl font-bold">{stats.population.toLocaleString()}</div>
            <div className="text-sm text-muted-foreground">Population</div>
          </div>
          <div className="text-center p-4 bg-muted rounded-lg">
            <Home className="h-6 w-6 mx-auto mb-2 text-muted-foreground" />
            <div className="text-2xl font-bold">{stats.households}</div>
            <div className="text-sm text-muted-foreground">Households</div>
          </div>
          <div className="text-center p-4 bg-muted rounded-lg">
            <Hospital className="h-6 w-6 mx-auto mb-2 text-muted-foreground" />
            <div className="text-2xl font-bold">{stats.healthcareFacilities}</div>
            <div className="text-sm text-muted-foreground">Health Centers</div>
          </div>
          <div className="text-center p-4 bg-muted rounded-lg">
            <School className="h-6 w-6 mx-auto mb-2 text-muted-foreground" />
            <div className="text-2xl font-bold">{stats.schoolsCount}</div>
            <div className="text-sm text-muted-foreground">Schools</div>
          </div>
        </div>

        {/* Development Indicators */}
        <div className="space-y-4">
          <h3 className="text-lg font-semibold">Development Indicators</h3>
          <div className="grid gap-4">
            {indicators.map((indicator) => {
              const Icon = indicator.icon
              return (
                <div key={indicator.label} className="space-y-2">
                  <div className="flex items-center justify-between">
                    <div className="flex items-center space-x-2">
                      <Icon className="h-4 w-4 text-muted-foreground" />
                      <span className="text-sm font-medium">{indicator.label}</span>
                    </div>
                    <div className="flex items-center space-x-2">
                      <span className={`text-sm font-bold ${getStatusColor(indicator.value, indicator.thresholds)}`}>
                        {indicator.value}
                        {indicator.unit}
                      </span>
                      {getStatusBadge(indicator.value, indicator.thresholds)}
                    </div>
                  </div>
                  <Progress value={indicator.value} className="h-2" />
                  <p className="text-xs text-muted-foreground">{indicator.description}</p>
                </div>
              )
            })}
          </div>
        </div>
      </CardContent>
    </Card>
  )
}
