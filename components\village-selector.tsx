"use client"

import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Button } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { MapPin, X } from "lucide-react"

interface VillageSelectorProps {
  selectedVillage: string
  onVillageSelect: (village: string) => void
  availableVillages: string[]
  comparisonVillages: string[]
  onComparisonChange: (villages: string[]) => void
}

export function VillageSelector({
  selectedVillage,
  onVillageSelect,
  availableVillages,
  comparisonVillages,
  onComparisonChange,
}: VillageSelectorProps) {
  const addComparisonVillage = (village: string) => {
    if (!comparisonVillages.includes(village) && village !== selectedVillage) {
      onComparisonChange([...comparisonVillages, village])
    }
  }

  const removeComparisonVillage = (village: string) => {
    onComparisonChange(comparisonVillages.filter((v) => v !== village))
  }

  const availableForComparison = availableVillages.filter(
    (v) => v !== selectedVillage && !comparisonVillages.includes(v),
  )

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center space-x-2">
          <MapPin className="h-5 w-5" />
          <span>Village Selection</span>
        </CardTitle>
        <CardDescription>Select a village to analyze and optionally add villages for comparison</CardDescription>
      </CardHeader>
      <CardContent className="space-y-4">
        <div>
          <label className="text-sm font-medium mb-2 block">Primary Village</label>
          <Select value={selectedVillage} onValueChange={onVillageSelect}>
            <SelectTrigger>
              <SelectValue placeholder="Select village for analysis" />
            </SelectTrigger>
            <SelectContent>
              {availableVillages.map((village) => (
                <SelectItem key={village} value={village}>
                  {village}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        </div>

        {selectedVillage && (
          <div>
            <div className="flex items-center justify-between mb-2">
              <label className="text-sm font-medium">Comparison Villages</label>
              {availableForComparison.length > 0 && (
                <Select onValueChange={addComparisonVillage}>
                  <SelectTrigger className="w-40">
                    <SelectValue placeholder="Add village" />
                  </SelectTrigger>
                  <SelectContent>
                    {availableForComparison.map((village) => (
                      <SelectItem key={village} value={village}>
                        {village}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              )}
            </div>

            {comparisonVillages.length > 0 ? (
              <div className="flex flex-wrap gap-2">
                {comparisonVillages.map((village) => (
                  <Badge key={village} variant="secondary" className="flex items-center space-x-1">
                    <span>{village}</span>
                    <Button
                      variant="ghost"
                      size="sm"
                      className="h-4 w-4 p-0 hover:bg-transparent"
                      onClick={() => removeComparisonVillage(village)}
                    >
                      <X className="h-3 w-3" />
                    </Button>
                  </Badge>
                ))}
              </div>
            ) : (
              <p className="text-sm text-muted-foreground">No comparison villages selected</p>
            )}
          </div>
        )}
      </CardContent>
    </Card>
  )
}
