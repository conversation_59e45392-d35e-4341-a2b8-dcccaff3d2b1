const express = require("express")
const multer = require("multer")
const router = express.Router()

// Configure multer for file uploads
const upload = multer({
  dest: "uploads/",
  limits: { fileSize: 10 * 1024 * 1024 }, // 10MB limit
})

// Mock OCR endpoint
router.post("/", upload.single("document"), (req, res) => {
  try {
    if (!req.file) {
      return res.status(400).json({ error: "No file uploaded" })
    }

    // Mock OCR results - in real implementation, this would call OCR service
    const mockResults = {
      holderName: "Ramesh <PERSON> Singh",
      village: "Kanha Village",
      district: "Mandla",
      state: "Madhya Pradesh",
      claimType: "IFR",
      extractedText: "Forest Rights Act Individual Forest Rights Claim...",
      confidence: 0.95,
      processingTime: "2.3s",
    }

    res.json({
      success: true,
      data: mockResults,
      message: "Document processed successfully",
    })
  } catch (error) {
    res.status(500).json({ error: "OCR processing failed" })
  }
})

module.exports = router
