"use client"

import type React from "react"

import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { useCallback, useState } from "react"
import { Satellite, ImageIcon, AlertCircle, MapPin } from "lucide-react"
import { Alert, AlertDescription } from "@/components/ui/alert"

interface SatelliteUploadProps {
  onImageUpload: (file: File, metadata: { village: string; district: string; state: string }) => void
}

export function SatelliteUpload({ onImageUpload }: SatelliteUploadProps) {
  const [dragActive, setDragActive] = useState(false)
  const [error, setError] = useState<string | null>(null)
  const [metadata, setMetadata] = useState({
    village: "",
    district: "",
    state: "",
  })

  const states = ["Madhya Pradesh", "Odisha", "Telangana", "Tripura"]
  const districts = {
    "Madhya Pradesh": ["Mandla", "Balaghat", "Dindori"],
    Odisha: ["Dhenkanal", "Mayurbhanj", "Keonjhar"],
    Telangana: ["Mulugu", "Bhadradri Kothagudem", "Jayashankar"],
    Tripura: ["Dhalai", "North Tripura", "South Tripura"],
  }

  const handleDrag = useCallback((e: React.DragEvent) => {
    e.preventDefault()
    e.stopPropagation()
    if (e.type === "dragenter" || e.type === "dragover") {
      setDragActive(true)
    } else if (e.type === "dragleave") {
      setDragActive(false)
    }
  }, [])

  const handleDrop = useCallback(
    (e: React.DragEvent) => {
      e.preventDefault()
      e.stopPropagation()
      setDragActive(false)
      setError(null)

      const files = Array.from(e.dataTransfer.files)
      if (files.length > 0) {
        const file = files[0]
        if (validateFile(file) && validateMetadata()) {
          onImageUpload(file, metadata)
        }
      }
    },
    [onImageUpload, metadata],
  )

  const handleFileInput = useCallback(
    (e: React.ChangeEvent<HTMLInputElement>) => {
      setError(null)
      const files = e.target.files
      if (files && files.length > 0) {
        const file = files[0]
        if (validateFile(file) && validateMetadata()) {
          onImageUpload(file, metadata)
        }
      }
    },
    [onImageUpload, metadata],
  )

  const validateFile = (file: File): boolean => {
    const maxSize = 50 * 1024 * 1024 // 50MB
    const allowedTypes = ["image/jpeg", "image/png", "image/tiff", "image/geotiff"]

    if (!allowedTypes.includes(file.type) && !file.name.toLowerCase().includes("tif")) {
      setError("Please upload a valid satellite image (JPEG, PNG, TIFF, GeoTIFF)")
      return false
    }

    if (file.size > maxSize) {
      setError("File size must be less than 50MB")
      return false
    }

    return true
  }

  const validateMetadata = (): boolean => {
    if (!metadata.village || !metadata.district || !metadata.state) {
      setError("Please fill in all location details before uploading")
      return false
    }
    return true
  }

  const availableDistricts = metadata.state ? districts[metadata.state as keyof typeof districts] || [] : []

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center space-x-2">
          <Satellite className="h-5 w-5" />
          <span>Upload Satellite Image</span>
        </CardTitle>
        <CardDescription>
          Upload high-resolution satellite images for AI-powered land use classification. Supported formats: JPEG, PNG,
          TIFF, GeoTIFF. Maximum file size: 50MB.
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-6">
        {/* Location Metadata */}
        <div className="space-y-4">
          <div className="flex items-center space-x-2 mb-4">
            <MapPin className="h-4 w-4" />
            <h3 className="text-sm font-medium">Location Information</h3>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div>
              <Label htmlFor="state">State</Label>
              <Select
                value={metadata.state}
                onValueChange={(value) => setMetadata({ ...metadata, state: value, district: "", village: "" })}
              >
                <SelectTrigger>
                  <SelectValue placeholder="Select state" />
                </SelectTrigger>
                <SelectContent>
                  {states.map((state) => (
                    <SelectItem key={state} value={state}>
                      {state}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            <div>
              <Label htmlFor="district">District</Label>
              <Select
                value={metadata.district}
                onValueChange={(value) => setMetadata({ ...metadata, district: value, village: "" })}
                disabled={!metadata.state}
              >
                <SelectTrigger>
                  <SelectValue placeholder="Select district" />
                </SelectTrigger>
                <SelectContent>
                  {availableDistricts.map((district) => (
                    <SelectItem key={district} value={district}>
                      {district}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            <div>
              <Label htmlFor="village">Village/Area</Label>
              <Input
                id="village"
                placeholder="Enter village name"
                value={metadata.village}
                onChange={(e) => setMetadata({ ...metadata, village: e.target.value })}
              />
            </div>
          </div>
        </div>

        {error && (
          <Alert variant="destructive">
            <AlertCircle className="h-4 w-4" />
            <AlertDescription>{error}</AlertDescription>
          </Alert>
        )}

        {/* File Upload Area */}
        <div
          className={`border-2 border-dashed rounded-lg p-8 text-center transition-colors ${
            dragActive ? "border-primary bg-primary/5" : "border-muted-foreground/25 hover:border-muted-foreground/50"
          }`}
          onDragEnter={handleDrag}
          onDragLeave={handleDrag}
          onDragOver={handleDrag}
          onDrop={handleDrop}
        >
          <div className="flex flex-col items-center space-y-4">
            <div className="flex space-x-2">
              <Satellite className="h-8 w-8 text-muted-foreground" />
              <ImageIcon className="h-8 w-8 text-muted-foreground" />
            </div>

            <div>
              <p className="text-lg font-medium">Drop your satellite image here</p>
              <p className="text-sm text-muted-foreground">or click to browse files</p>
            </div>

            <input
              type="file"
              accept=".jpg,.jpeg,.png,.tiff,.tif,.geotiff"
              onChange={handleFileInput}
              className="hidden"
              id="satellite-upload"
            />

            <Button asChild disabled={!metadata.village || !metadata.district || !metadata.state}>
              <label htmlFor="satellite-upload" className="cursor-pointer">
                Choose Satellite Image
              </label>
            </Button>

            <div className="text-xs text-muted-foreground space-y-1">
              <p>Supported formats: JPEG, PNG, TIFF, GeoTIFF</p>
              <p>Maximum file size: 50MB</p>
              <p>Recommended resolution: 10m/pixel or higher</p>
            </div>
          </div>
        </div>

        {/* Feature Highlights */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
          <div className="flex items-center space-x-2">
            <div className="w-2 h-2 bg-green-500 rounded-full"></div>
            <span>AI-powered classification</span>
          </div>
          <div className="flex items-center space-x-2">
            <div className="w-2 h-2 bg-blue-500 rounded-full"></div>
            <span>Multi-spectral analysis</span>
          </div>
          <div className="flex items-center space-x-2">
            <div className="w-2 h-2 bg-purple-500 rounded-full"></div>
            <span>Land use mapping</span>
          </div>
          <div className="flex items-center space-x-2">
            <div className="w-2 h-2 bg-orange-500 rounded-full"></div>
            <span>Area calculations</span>
          </div>
        </div>
      </CardContent>
    </Card>
  )
}
