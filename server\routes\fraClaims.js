const express = require("express")
const FraClaim = require("../models/FraClaim")
const router = express.Router()

// Get all FRA claims with optional filtering
router.get("/", async (req, res) => {
  try {
    const { state, district, village, claimType } = req.query
    const filter = {}

    if (state) filter.state = state
    if (district) filter.district = district
    if (village) filter.village = village
    if (claimType) filter.claimType = claimType

    const claims = await FraClaim.find(filter)
    res.json({ success: true, data: claims })
  } catch (error) {
    res.status(500).json({ error: "Failed to fetch FRA claims" })
  }
})

// Create new FRA claim
router.post("/", async (req, res) => {
  try {
    const claim = new FraClaim(req.body)
    await claim.save()
    res.status(201).json({ success: true, data: claim })
  } catch (error) {
    res.status(400).json({ error: "Failed to create FRA claim" })
  }
})

module.exports = router
