"use client"

import { <PERSON>, CardContent, CardDescription, Card<PERSON><PERSON>er, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>ist, Ta<PERSON>Trigger } from "@/components/ui/tabs"
import type { SchemeRecommendation, VillageData } from "@/app/dss/page"
import {
  Droplets,
  Wheat,
  Trees,
  Users,
  Bold as Road,
  Hospital,
  GraduationCap,
  Download,
  AlertTriangle,
  CheckCircle,
  Clock,
} from "lucide-react"

interface SchemeRecommendationsProps {
  recommendations: SchemeRecommendation[]
  villageData: VillageData
}

export function SchemeRecommendations({ recommendations, villageData }: SchemeRecommendationsProps) {
  const getCategoryIcon = (category: string) => {
    switch (category) {
      case "Water":
        return Droplets
      case "Agriculture":
        return Wheat
      case "Forest":
        return Trees
      case "Livelihood":
        return Users
      case "Infrastructure":
        return Road
      case "Health":
        return Hospital
      case "Education":
        return GraduationCap
      default:
        return Users
    }
  }

  const getPriorityIcon = (priority: string) => {
    switch (priority) {
      case "High":
        return <AlertTriangle className="h-4 w-4 text-red-500" />
      case "Medium":
        return <Clock className="h-4 w-4 text-yellow-500" />
      case "Low":
        return <CheckCircle className="h-4 w-4 text-green-500" />
      default:
        return <Clock className="h-4 w-4" />
    }
  }

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case "High":
        return "destructive"
      case "Medium":
        return "secondary"
      case "Low":
        return "default"
      default:
        return "secondary"
    }
  }

  const groupedRecommendations = recommendations.reduce(
    (acc, rec) => {
      if (!acc[rec.priority]) acc[rec.priority] = []
      acc[rec.priority].push(rec)
      return acc
    },
    {} as Record<string, SchemeRecommendation[]>,
  )

  const handleExportRecommendations = () => {
    const exportData = {
      village: villageData.village,
      district: villageData.district,
      state: villageData.state,
      analysisDate: new Date().toISOString(),
      socioEconomicStats: villageData.socioEconomicStats,
      recommendations: recommendations.map((rec) => ({
        scheme: rec.scheme,
        priority: rec.priority,
        category: rec.category,
        reason: rec.reason,
        estimatedBeneficiaries: rec.estimatedBeneficiaries,
        implementationCost: rec.implementationCost,
        timeframe: rec.timeframe,
      })),
      summary: {
        totalRecommendations: recommendations.length,
        highPriority: recommendations.filter((r) => r.priority === "High").length,
        totalBeneficiaries: recommendations.reduce((sum, r) => sum + r.estimatedBeneficiaries, 0),
      },
    }

    const blob = new Blob([JSON.stringify(exportData, null, 2)], { type: "application/json" })
    const url = URL.createObjectURL(blob)
    const a = document.createElement("a")
    a.href = url
    a.download = `${villageData.village}_DSS_recommendations.json`
    document.body.appendChild(a)
    a.click()
    document.body.removeChild(a)
    URL.revokeObjectURL(url)
  }

  return (
    <Card>
      <CardHeader>
        <div className="flex items-center justify-between">
          <div>
            <CardTitle>Scheme Recommendations</CardTitle>
            <CardDescription>
              Data-driven policy recommendations for {villageData.village} ({recommendations.length} schemes identified)
            </CardDescription>
          </div>
          <Button variant="outline" size="sm" onClick={handleExportRecommendations}>
            <Download className="h-4 w-4 mr-2" />
            Export
          </Button>
        </div>
      </CardHeader>
      <CardContent>
        <Tabs defaultValue="all" className="w-full">
          <TabsList className="grid w-full grid-cols-4">
            <TabsTrigger value="all">All ({recommendations.length})</TabsTrigger>
            <TabsTrigger value="High">High Priority ({groupedRecommendations.High?.length || 0})</TabsTrigger>
            <TabsTrigger value="Medium">Medium ({groupedRecommendations.Medium?.length || 0})</TabsTrigger>
            <TabsTrigger value="Low">Low ({groupedRecommendations.Low?.length || 0})</TabsTrigger>
          </TabsList>

          <TabsContent value="all" className="space-y-4">
            {recommendations.map((recommendation, index) => {
              const Icon = getCategoryIcon(recommendation.category)
              return (
                <Card key={index} className="border-l-4 border-l-primary">
                  <CardContent className="p-4">
                    <div className="flex items-start justify-between mb-3">
                      <div className="flex items-center space-x-2">
                        <Icon className="h-5 w-5 text-muted-foreground" />
                        <h3 className="font-semibold">{recommendation.scheme}</h3>
                      </div>
                      <div className="flex items-center space-x-2">
                        {getPriorityIcon(recommendation.priority)}
                        <Badge variant={getPriorityColor(recommendation.priority) as any}>
                          {recommendation.priority} Priority
                        </Badge>
                      </div>
                    </div>

                    <p className="text-sm text-muted-foreground mb-3">{recommendation.description}</p>

                    <div className="bg-muted p-3 rounded-lg mb-3">
                      <p className="text-sm">
                        <strong>Rationale:</strong> {recommendation.reason}
                      </p>
                    </div>

                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
                      <div>
                        <strong>Estimated Beneficiaries:</strong>
                        <span className="ml-2">{recommendation.estimatedBeneficiaries.toLocaleString()}</span>
                      </div>
                      <div>
                        <strong>Category:</strong>
                        <span className="ml-2">{recommendation.category}</span>
                      </div>
                      {recommendation.implementationCost && (
                        <div>
                          <strong>Implementation Cost:</strong>
                          <span className="ml-2">{recommendation.implementationCost}</span>
                        </div>
                      )}
                      {recommendation.timeframe && (
                        <div>
                          <strong>Timeframe:</strong>
                          <span className="ml-2">{recommendation.timeframe}</span>
                        </div>
                      )}
                    </div>

                    <div className="mt-3">
                      <strong className="text-sm">Eligibility:</strong>
                      <div className="flex flex-wrap gap-1 mt-1">
                        {recommendation.eligibility.map((criteria, idx) => (
                          <Badge key={idx} variant="outline" className="text-xs">
                            {criteria}
                          </Badge>
                        ))}
                      </div>
                    </div>
                  </CardContent>
                </Card>
              )
            })}
          </TabsContent>

          {["High", "Medium", "Low"].map((priority) => (
            <TabsContent key={priority} value={priority} className="space-y-4">
              {groupedRecommendations[priority]?.map((recommendation, index) => {
                const Icon = getCategoryIcon(recommendation.category)
                return (
                  <Card key={index} className="border-l-4 border-l-primary">
                    <CardContent className="p-4">
                      <div className="flex items-start justify-between mb-3">
                        <div className="flex items-center space-x-2">
                          <Icon className="h-5 w-5 text-muted-foreground" />
                          <h3 className="font-semibold">{recommendation.scheme}</h3>
                        </div>
                        <Badge variant={getPriorityColor(recommendation.priority) as any}>
                          {recommendation.priority} Priority
                        </Badge>
                      </div>

                      <p className="text-sm text-muted-foreground mb-3">{recommendation.description}</p>

                      <div className="bg-muted p-3 rounded-lg mb-3">
                        <p className="text-sm">
                          <strong>Rationale:</strong> {recommendation.reason}
                        </p>
                      </div>

                      <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
                        <div>
                          <strong>Estimated Beneficiaries:</strong>
                          <span className="ml-2">{recommendation.estimatedBeneficiaries.toLocaleString()}</span>
                        </div>
                        <div>
                          <strong>Category:</strong>
                          <span className="ml-2">{recommendation.category}</span>
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                )
              }) || (
                <p className="text-muted-foreground text-center py-8">
                  No {priority.toLowerCase()} priority recommendations
                </p>
              )}
            </TabsContent>
          ))}
        </Tabs>
      </CardContent>
    </Card>
  )
}
