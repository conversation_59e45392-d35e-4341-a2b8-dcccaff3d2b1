"use client"

import { <PERSON>, CardContent, CardDescription, Card<PERSON><PERSON>er, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Avatar, AvatarFallback } from "@/components/ui/avatar"
import { FileText, MapPin, Satellite, BarChart3 } from "lucide-react"

const activities = [
  {
    id: 1,
    type: "ocr",
    title: "Document processed for Kanha Village",
    description: "IFR claim document digitized with 95% accuracy",
    time: "2 minutes ago",
    status: "completed",
    icon: FileText,
  },
  {
    id: 2,
    type: "mapping",
    title: "New FRA claim mapped in Telangana",
    description: "CFR claim added to Eturnagaram village",
    time: "15 minutes ago",
    status: "completed",
    icon: MapPin,
  },
  {
    id: 3,
    type: "satellite",
    title: "Satellite image analysis completed",
    description: "Land use classification for Bamni Village",
    time: "1 hour ago",
    status: "completed",
    icon: Satellite,
  },
  {
    id: 4,
    type: "dss",
    title: "DSS recommendations generated",
    description: "3 new scheme recommendations for MP region",
    time: "2 hours ago",
    status: "completed",
    icon: BarChart3,
  },
  {
    id: 5,
    type: "ocr",
    title: "Batch processing initiated",
    description: "25 documents queued for OCR processing",
    time: "3 hours ago",
    status: "processing",
    icon: FileText,
  },
]

export function RecentActivities() {
  return (
    <Card>
      <CardHeader>
        <CardTitle>Recent Activities</CardTitle>
        <CardDescription>Latest system activities and processing updates</CardDescription>
      </CardHeader>
      <CardContent className="space-y-4">
        {activities.map((activity) => {
          const Icon = activity.icon
          return (
            <div key={activity.id} className="flex items-start space-x-4">
              <Avatar className="h-8 w-8">
                <AvatarFallback className="bg-muted">
                  <Icon className="h-4 w-4" />
                </AvatarFallback>
              </Avatar>
              <div className="flex-1 space-y-1">
                <div className="flex items-center justify-between">
                  <p className="text-sm font-medium">{activity.title}</p>
                  <Badge variant={activity.status === "completed" ? "default" : "secondary"} className="text-xs">
                    {activity.status}
                  </Badge>
                </div>
                <p className="text-xs text-muted-foreground">{activity.description}</p>
                <p className="text-xs text-muted-foreground">{activity.time}</p>
              </div>
            </div>
          )
        })}
      </CardContent>
    </Card>
  )
}
