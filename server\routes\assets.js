const express = require("express")
const multer = require("multer")
const router = express.Router()

const upload = multer({
  dest: "uploads/",
  limits: { fileSize: 50 * 1024 * 1024 }, // 50MB limit for satellite images
})

// Mock Asset Classification endpoint
router.post("/", upload.single("satellite_image"), (req, res) => {
  try {
    if (!req.file) {
      return res.status(400).json({ error: "No image uploaded" })
    }

    // Mock AI classification results
    const mockClassification = {
      village: "Kanha Village",
      totalArea: 1250.5, // in hectares
      classification: {
        forest: { area: 750.2, percentage: 60.0, color: "#228B22" },
        water: { area: 125.1, percentage: 10.0, color: "#4169E1" },
        agricultural: { area: 250.1, percentage: 20.0, color: "#FFD700" },
        residential: { area: 75.1, percentage: 6.0, color: "#FF6347" },
        barren: { area: 50.0, percentage: 4.0, color: "#D2691E" },
      },
      confidence: 0.92,
      processingTime: "15.7s",
    }

    res.json({
      success: true,
      data: mockClassification,
      message: "Satellite image classified successfully",
    })
  } catch (error) {
    res.status(500).json({ error: "Asset classification failed" })
  }
})

module.exports = router
