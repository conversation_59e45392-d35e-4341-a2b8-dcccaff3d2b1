import { Navigation } from "@/components/navigation"
import { Button } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { DashboardStats } from "@/components/dashboard-stats"
import { RecentActivities } from "@/components/recent-activities"
import { SystemStatus } from "@/components/system-status"
import Link from "next/link"

export default function HomePage() {
  return (
    <div className="min-h-screen bg-background">
      <Navigation />

      <main className="container mx-auto px-4 py-8">
        {/* Hero Section */}
        <div className="text-center mb-12">
          <h1 className="text-4xl font-bold tracking-tight mb-4 text-balance">FRA Atlas & Decision Support System</h1>
          <p className="text-xl text-muted-foreground max-w-2xl mx-auto text-pretty">
            AI-powered Forest Rights Act Atlas with WebGIS-based Decision Support System for efficient land rights
            management and policy implementation.
          </p>
        </div>

        <div className="mb-8">
          <DashboardStats />
        </div>

        {/* Feature Cards */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-12">
          <Card className="hover:shadow-lg transition-shadow">
            <CardHeader>
              <CardTitle className="text-lg">FRA Atlas</CardTitle>
              <CardDescription>Interactive WebGIS mapping of Forest Rights Act claims and boundaries</CardDescription>
            </CardHeader>
            <CardContent>
              <Button asChild className="w-full">
                <Link href="/fra-atlas">View Atlas</Link>
              </Button>
            </CardContent>
          </Card>

          <Card className="hover:shadow-lg transition-shadow">
            <CardHeader>
              <CardTitle className="text-lg">Document Digitization</CardTitle>
              <CardDescription>OCR-powered extraction of data from scanned FRA documents</CardDescription>
            </CardHeader>
            <CardContent>
              <Button asChild className="w-full">
                <Link href="/digitization">Upload Documents</Link>
              </Button>
            </CardContent>
          </Card>

          <Card className="hover:shadow-lg transition-shadow">
            <CardHeader>
              <CardTitle className="text-lg">Asset Mapping</CardTitle>
              <CardDescription>AI-based classification of satellite imagery for land use analysis</CardDescription>
            </CardHeader>
            <CardContent>
              <Button asChild className="w-full">
                <Link href="/asset-mapping">Map Assets</Link>
              </Button>
            </CardContent>
          </Card>

          <Card className="hover:shadow-lg transition-shadow">
            <CardHeader>
              <CardTitle className="text-lg">Decision Support</CardTitle>
              <CardDescription>Data-driven recommendations for policy and scheme implementation</CardDescription>
            </CardHeader>
            <CardContent>
              <Button asChild className="w-full">
                <Link href="/dss">View DSS</Link>
              </Button>
            </CardContent>
          </Card>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-8">
          <RecentActivities />
          <SystemStatus />
        </div>

        {/* Stats Section */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
          <div className="text-center p-6 bg-muted rounded-lg">
            <div className="text-3xl font-bold text-primary mb-2">2,500+</div>
            <div className="text-sm text-muted-foreground">FRA Claims Mapped</div>
          </div>
          <div className="text-center p-6 bg-muted rounded-lg">
            <div className="text-3xl font-bold text-primary mb-2">4</div>
            <div className="text-sm text-muted-foreground">States Covered</div>
          </div>
          <div className="text-center p-6 bg-muted rounded-lg">
            <div className="text-3xl font-bold text-primary mb-2">95%</div>
            <div className="text-sm text-muted-foreground">OCR Accuracy</div>
          </div>
        </div>
      </main>
    </div>
  )
}
