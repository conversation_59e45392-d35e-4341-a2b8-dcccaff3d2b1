"use client"

import { <PERSON>, Card<PERSON>ontent, CardDescription, Card<PERSON><PERSON>er, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { ScrollArea } from "@/components/ui/scroll-area"
import type { OCRResult } from "@/app/digitization/page"
import { FileText, Clock, CheckCircle, XCircle } from "lucide-react"

interface ProcessingHistoryProps {
  results: OCRResult[]
  selectedResult: OCRResult | null
  onResultSelect: (result: OCRResult) => void
}

export function ProcessingHistory({ results, selectedResult, onResultSelect }: ProcessingHistoryProps) {
  const getStatusIcon = (status: string) => {
    switch (status) {
      case "completed":
        return <CheckCircle className="h-4 w-4 text-green-500" />
      case "processing":
        return <Clock className="h-4 w-4 text-yellow-500" />
      case "error":
        return <XCircle className="h-4 w-4 text-red-500" />
      default:
        return <FileText className="h-4 w-4" />
    }
  }

  const formatDate = (date: Date) => {
    return new Intl.DateTimeFormat("en-US", {
      month: "short",
      day: "numeric",
      hour: "2-digit",
      minute: "2-digit",
    }).format(date)
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center space-x-2">
          <Clock className="h-5 w-5" />
          <span>Processing History</span>
        </CardTitle>
        <CardDescription>Recent document processing results ({results.length} total)</CardDescription>
      </CardHeader>
      <CardContent>
        <ScrollArea className="h-[600px]">
          {results.length === 0 ? (
            <div className="text-center py-8 text-muted-foreground">
              <FileText className="h-8 w-8 mx-auto mb-2 opacity-50" />
              <p>No documents processed yet</p>
              <p className="text-sm">Upload a document to get started</p>
            </div>
          ) : (
            <div className="space-y-3">
              {results.map((result) => (
                <div
                  key={result.id}
                  className={`p-3 rounded-lg border cursor-pointer transition-colors ${
                    selectedResult?.id === result.id ? "border-primary bg-primary/5" : "border-border hover:bg-muted/50"
                  }`}
                  onClick={() => onResultSelect(result)}
                >
                  <div className="flex items-start justify-between mb-2">
                    <div className="flex items-center space-x-2">
                      {getStatusIcon(result.status)}
                      <span className="text-sm font-medium truncate">{result.fileName}</span>
                    </div>
                    <Badge variant="secondary" className="text-xs">
                      {Math.round(result.confidence * 100)}%
                    </Badge>
                  </div>

                  <div className="space-y-1 text-xs text-muted-foreground">
                    <p>
                      <strong>Holder:</strong> {result.holderName}
                    </p>
                    <p>
                      <strong>Location:</strong> {result.village}, {result.district}
                    </p>
                    <p>
                      <strong>Type:</strong> {result.claimType}
                    </p>
                  </div>

                  <div className="flex items-center justify-between mt-2 text-xs text-muted-foreground">
                    <span>{formatDate(result.timestamp)}</span>
                    <span>{result.processingTime}</span>
                  </div>
                </div>
              ))}
            </div>
          )}
        </ScrollArea>

        {results.length > 0 && (
          <div className="mt-4 pt-4 border-t">
            <Button variant="outline" size="sm" className="w-full bg-transparent">
              Export All Results
            </Button>
          </div>
        )}
      </CardContent>
    </Card>
  )
}
