"use client"

import { Navigation } from "@/components/navigation"
import { MapContainer } from "@/components/map-container"
import { MapSidebar } from "@/components/map-sidebar"
import { useState } from "react"

export interface MapFilters {
  state: string
  district: string
  village: string
  claimType: string
  status: string
}

export default function FRAAtlasPage() {
  const [filters, setFilters] = useState<MapFilters>({
    state: "",
    district: "",
    village: "",
    claimType: "",
    status: "",
  })

  const [selectedLayers, setSelectedLayers] = useState({
    boundaries: true,
    fraClaims: true,
    landUse: false,
  })

  return (
    <div className="min-h-screen bg-background">
      <Navigation />

      <div className="flex h-[calc(100vh-4rem)]">
        <MapSidebar
          filters={filters}
          setFilters={setFilters}
          selectedLayers={selectedLayers}
          setSelectedLayers={setSelectedLayers}
        />

        <div className="flex-1">
          <MapContainer filters={filters} selectedLayers={selectedLayers} />
        </div>
      </div>
    </div>
  )
}
