"use client"

import { Navigation } from "@/components/navigation"
import { DocumentUpload } from "@/components/document-upload"
import { ProcessingStatus } from "@/components/processing-status"
import { ExtractionResults } from "@/components/extraction-results"
import { ProcessingHistory } from "@/components/processing-history"
import { useState } from "react"

export interface OCRResult {
  id: string
  fileName: string
  holderName: string
  village: string
  district: string
  state: string
  claimType: "IFR" | "CR" | "CFR"
  extractedText: string
  confidence: number
  processingTime: string
  timestamp: Date
  status: "processing" | "completed" | "error"
}

export default function DigitizationPage() {
  const [currentProcessing, setCurrentProcessing] = useState<{
    fileName: string
    progress: number
    status: "uploading" | "processing" | "completed" | "error"
  } | null>(null)

  const [results, setResults] = useState<OCRResult[]>([])
  const [selectedResult, setSelectedResult] = useState<OCRResult | null>(null)

  const handleFileUpload = async (file: File) => {
    const fileName = file.name
    setCurrentProcessing({
      fileName,
      progress: 0,
      status: "uploading",
    })

    // Simulate upload progress
    for (let i = 0; i <= 100; i += 10) {
      await new Promise((resolve) => setTimeout(resolve, 100))
      setCurrentProcessing((prev) => prev && { ...prev, progress: i })
    }

    setCurrentProcessing((prev) => prev && { ...prev, status: "processing" })

    // Simulate OCR processing
    await new Promise((resolve) => setTimeout(resolve, 2000))

    // Mock OCR result
    const mockResult: OCRResult = {
      id: Date.now().toString(),
      fileName,
      holderName: "Ramesh Kumar Singh",
      village: "Kanha Village",
      district: "Mandla",
      state: "Madhya Pradesh",
      claimType: "IFR",
      extractedText: `Forest Rights Act - Individual Forest Rights Claim
      
Claim Holder: Ramesh Kumar Singh
Father's Name: Mohan Singh
Village: Kanha Village
District: Mandla
State: Madhya Pradesh
Claim Type: Individual Forest Rights (IFR)
Area Claimed: 2.5 hectares
Survey Number: 123/4
Occupation Since: 1985
Purpose: Cultivation and habitation

The above mentioned person has been residing in the forest area and has been cultivating the land for more than 75 years. The claim is being made under Section 3(1)(a) of the Forest Rights Act, 2006.`,
      confidence: 0.95,
      processingTime: "2.3s",
      timestamp: new Date(),
      status: "completed",
    }

    setResults((prev) => [mockResult, ...prev])
    setSelectedResult(mockResult)
    setCurrentProcessing((prev) => prev && { ...prev, status: "completed" })

    // Clear processing status after a delay
    setTimeout(() => {
      setCurrentProcessing(null)
    }, 2000)
  }

  return (
    <div className="min-h-screen bg-background">
      <Navigation />

      <main className="container mx-auto px-4 py-8">
        <div className="mb-8">
          <h1 className="text-3xl font-bold tracking-tight mb-2">Document Digitization</h1>
          <p className="text-muted-foreground">Upload scanned FRA documents for OCR processing and data extraction</p>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
          {/* Upload Section */}
          <div className="lg:col-span-2 space-y-6">
            <DocumentUpload onFileUpload={handleFileUpload} />

            {currentProcessing && <ProcessingStatus processing={currentProcessing} />}

            {selectedResult && (
              <ExtractionResults
                result={selectedResult}
                onResultUpdate={(updatedResult) => {
                  setResults((prev) => prev.map((r) => (r.id === updatedResult.id ? updatedResult : r)))
                  setSelectedResult(updatedResult)
                }}
              />
            )}
          </div>

          {/* History Section */}
          <div>
            <ProcessingHistory results={results} selectedResult={selectedResult} onResultSelect={setSelectedResult} />
          </div>
        </div>
      </main>
    </div>
  )
}
