"use client"

import { <PERSON>, CardContent, CardDescription, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from "@/components/ui/card"
import { Progress } from "@/components/ui/progress"
import { Badge } from "@/components/ui/badge"
import { <PERSON><PERSON>ircle, AlertCircle, Clock } from "lucide-react"

const systemServices = [
  {
    name: "OCR Service",
    status: "operational",
    uptime: 99.8,
    description: "Document processing and text extraction",
  },
  {
    name: "GIS Engine",
    status: "operational",
    uptime: 99.9,
    description: "Mapping and spatial data processing",
  },
  {
    name: "AI Classification",
    status: "operational",
    uptime: 98.5,
    description: "Satellite image analysis and land use classification",
  },
  {
    name: "Database",
    status: "operational",
    uptime: 99.7,
    description: "MongoDB cluster for data storage",
  },
  {
    name: "DSS Engine",
    status: "maintenance",
    uptime: 95.2,
    description: "Decision support system and recommendations",
  },
]

export function SystemStatus() {
  return (
    <Card>
      <CardHeader>
        <CardTitle>System Status</CardTitle>
        <CardDescription>Real-time status of all system components</CardDescription>
      </CardHeader>
      <CardContent className="space-y-4">
        {systemServices.map((service) => (
          <div key={service.name} className="space-y-2">
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-2">
                {service.status === "operational" ? (
                  <CheckCircle className="h-4 w-4 text-green-500" />
                ) : service.status === "maintenance" ? (
                  <Clock className="h-4 w-4 text-yellow-500" />
                ) : (
                  <AlertCircle className="h-4 w-4 text-red-500" />
                )}
                <span className="text-sm font-medium">{service.name}</span>
              </div>
              <Badge
                variant={
                  service.status === "operational"
                    ? "default"
                    : service.status === "maintenance"
                      ? "secondary"
                      : "destructive"
                }
              >
                {service.status}
              </Badge>
            </div>
            <p className="text-xs text-muted-foreground">{service.description}</p>
            <div className="flex items-center space-x-2">
              <Progress value={service.uptime} className="flex-1" />
              <span className="text-xs text-muted-foreground">{service.uptime}%</span>
            </div>
          </div>
        ))}
      </CardContent>
    </Card>
  )
}
